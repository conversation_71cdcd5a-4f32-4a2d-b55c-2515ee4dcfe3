import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import torch
from math import ceil

# 从原始notebook复制必要的函数
beta_base = [1/3, -2/3, 1, -1/3, 2/3, -1]

def gen_Cov(p, rho):
    cov = np.ones((p, p))*rho
    for i in range(p):
        cov[i, i] = 1
    return cov

def get_beta_s(p_s):
    beta_s = beta_base * (ceil(p_s/len(beta_base)))
    return np.array(beta_s[:p_s])


def _gen_data(p=10, r=1.7, n_single=2000, V_ratio=0.5, Vb_ratio=0.1, gener_method='cox_exp', true_func="linear",
              mlp=None, mode="S_|_V", misspe="poly", mms_strength=1.0, corr_s=0.9, corr_v=0.1, spurious="nonlinear",
              noise_variance=0.3, device=None, **options):
    '''
    p: dim of input X
    r: bias rate
    n: number of samples
    '''
    # dim of S, V, V_b
    p_v = int(p * V_ratio)
    p_s = p - p_v
    p_b = int(p * Vb_ratio)

    # generate covariates
    Z = np.random.randn(n_single, p)
    S = np.zeros((n_single, p_s))
    V = np.zeros((n_single, p_v))
    if mode == "S_|_V":
        V = np.random.randn(n_single, p_v)
        for i in range(p_s):
            S[:, i] = 0.8 * Z[:, i] + 0.2 * Z[:, i + 1]  # hard-coding
    elif mode == "S->V":
        for i in range(p_s):
            S[:, i] = 0.8 * Z[:, i] + 0.2 * Z[:, i + 1]
        for j in range(p_v):
            V[:, j] = 0.8 * S[:, j] + 0.2 * S[:, (j + 1) % (p_s)] + np.random.randn(n_single)
    elif mode == "V->S":
        V = np.random.randn(n_single, p_v)
        for j in range(p_s):
            S[:, j] = 0.2 * V[:, j] + 0.8 * V[:, (j + 1) % (p_v)] + np.random.randn(n_single)
    elif mode == "collinearity":
        Sigma_s = gen_Cov(p_s, corr_s)
        Sigma_v = gen_Cov(p_v, corr_v)
        S = np.random.multivariate_normal([0] * p_s, Sigma_s, n_single)
        V = np.random.multivariate_normal([0] * p_v, Sigma_v, n_single)
    else:
        raise NotImplementedError

    # generate f(S)
    if true_func == "linear":
        beta_s = get_beta_s(p_s)
        beta_s = np.reshape(beta_s, (-1, 1))
        linear_term = np.matmul(S, beta_s)
        if misspe == "poly":  # hard-coding: S1·S2·S3
            nonlinear_term = np.reshape(np.prod(S[:, :3], axis=1), (-1, 1))
        elif misspe == "exp":  # hard-coding: exp(S1·S2·S3)
            nonlinear_term = np.reshape(np.exp(np.prod(S[:, :3], axis=1)), (-1, 1))
        elif misspe == "None":
            nonlinear_term = 0
        else:
            raise NotImplementedError
        fs = linear_term + mms_strength * nonlinear_term
    elif true_func == "MLP":
        fs = mlp(torch.tensor(S, dtype=torch.float, device=device)).detach().cpu().numpy()
    elif true_func == "poly":
        fs = np.reshape(np.prod(S, axis=1), (-1, 1))
    elif true_func == "exp":
        fs = np.reshape(np.exp(np.prod(S, axis=1)), (-1, 1))
    else:
        raise NotImplementedError

    # generate spurious correlation
    if spurious == "nonlinear":
        D = np.abs(fs - r / abs(r) * V[:, -p_b:])  # dim: (n, p_b), select the last p_b dim of V as V_b
    elif spurious == "linear":
        D = np.abs(linear_term - r / abs(r) * V[:, -p_b:])
    else:
        raise NotImplementedError
    Pr = np.power(abs(r), -5 * np.sum(D, axis=1))  # probability of being selected for certain samples
    select = np.random.uniform(size=Pr.shape[0]) < Pr
    # select
    S = S[select, :]
    V = V[select, :]
    X = np.concatenate((S, V), axis=1)
    fs = fs[select, :]

    lambda_param = fs  # + np.random.randn(*fs.shape)*np.sqrt(noise_variance)
    lambda_param = lambda_param.reshape(lambda_param.shape[0])

    U = np.random.uniform(0, 1, size=lambda_param.shape[0])
    # noise = (np.random.rand(*lambda_param.shape)*np.sqrt(noise_variance)).reshape(-1)
    if gener_method == "cox_exp":
        tmp = np.exp(-lambda_param).reshape(-1)
        noise = (np.random.rand(*lambda_param.shape) * np.sqrt(noise_variance)).reshape(-1)
        Y = 1 / 0.5 * (-np.log(U) * tmp)  # + noise
    elif gener_method == "cox_weibull":
        tmp = np.exp(-lambda_param).reshape(-1)
        Y = np.power(-1 / 0.5 * np.log(U) * tmp, 4)
    elif gener_method == "cox_Gompertz":
        Y = 1 / 2 * np.log(1 - np.log(U) / np.exp(lambda_param))
    elif gener_method == "exp_T":
        noise = np.random.exponential(scale=1, size=lambda_param.shape[0])
        Y = np.exp(lambda_param) + noise
    elif gener_method == "log_T":
        noise = np.random.normal(0, 0.5, size=lambda_param.shape[0])
        Y = np.exp(lambda_param + noise)
    elif gener_method == "poly":
        # lambda_param = lambda_param + abs(min(lambda_param)) + 0.001
        select = lambda_param > 0
        lambda_param = lambda_param[select]
        U = np.random.uniform(0, 1, size=lambda_param.shape[0])
        S = S[select, :]
        V = V[select, :]
        X = np.concatenate((S, V), axis=1)
        Y = -np.log(U) / lambda_param
    return X, S, V, fs, Y

def gen_selection_bias_data(args):
    n_total = args["n"]
    n_cur = 0
    S_list = []
    V_list = []
    fs_list = []
    Y_list = []
    while n_cur < n_total:
        _, S, V, fs, Y = _gen_data(n_single=n_total, **args)
        S_list.append(S)
        V_list.append(V)
        fs_list.append(fs)
        Y_list.append(Y)
        n_cur += Y.shape[0]
    S = np.concatenate(S_list, axis=0)[:n_total]
    V = np.concatenate(V_list, axis=0)[:n_total]
    fs = np.concatenate(fs_list, axis=0)[:n_total]
    Y = np.concatenate(Y_list, axis=0)[:n_total]
    X = np.concatenate((S, V), axis=1)

    return X, S, V, fs, Y

def main():
    # 定义r值列表
    r_values = [-3, -2, -1.7, -1.5, -1.3, 1.3, 1.5, 1.7, 2, 3]
    
    # 设置英文字体
    plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 创建两个子图
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(18, 8))
    
    # 存储所有结果用于后续分析
    all_results = []
    
    # 定义颜色映射
    colors = plt.cm.tab10(np.linspace(0, 1, len(r_values)))
    
    for i, r in enumerate(r_values):
        print(f"Processing r = {r}")
        
        # 为每个r值生成数据
        args = {
            "p": 10,
            "r": r,
            "n": 10000,
            "mode": "S_|_V",
            "misspe": "poly"
        }
        
        X, S, V, fs, Y = gen_selection_bias_data(args)
        S1 = S[:, 0]
        V1 = V[:, 0]
        
        # 存储结果
        all_results.append({
            'r': r,
            'S1': S1,
            'V1': V1,
            'fs': fs.flatten(),
            'Y': Y,
            'sample_size': len(Y)
        })
        
        # 绘制S1的kde
        sns.kdeplot(data=S1, alpha=0.8, color=colors[i], label=f'r={r}', ax=ax1, linewidth=2)
        # 绘制V1的kde
        sns.kdeplot(data=V1, alpha=0.8, color=colors[i], label=f'r={r}', ax=ax2, linewidth=2)
    
    # 设置图表标题和标签
    ax1.set_xlabel('S1 Value')
    ax1.set_ylabel('Density')
    ax1.set_title('S1 Distribution (KDE) for Different r')
    ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    ax1.grid(True, alpha=0.3)
    
    ax2.set_xlabel('V1 Value')
    ax2.set_ylabel('Density')
    ax2.set_title('V1 Distribution (KDE) for Different r')
    ax2.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('r_traversal_S1_V1_kdeplots.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 打印每个r值的统计信息
    print("\n=== Statistics for Different r Values ===")
    for result in all_results:
        r = result['r']
        print(f"\nr = {r}:")
        print(f"  S1 mean: {np.mean(result['S1']):.4f}, std: {np.std(result['S1']):.4f}")
        print(f"  V1 mean: {np.mean(result['V1']):.4f}, std: {np.std(result['V1']):.4f}")
        print(f"  Y mean: {np.mean(result['Y']):.4f}, std: {np.std(result['Y']):.4f}")
        print(f"  fs mean: {np.mean(result['fs']):.4f}, std: {np.std(result['fs']):.4f}")
        print(f"  sample size: {result['sample_size']}")
    
    # 创建汇总统计表
    summary_data = []
    for result in all_results:
        summary_data.append({
            'r': result['r'],
            'S1_mean': np.mean(result['S1']),
            'S1_std': np.std(result['S1']),
            'V1_mean': np.mean(result['V1']),
            'V1_std': np.std(result['V1']),
            'Y_mean': np.mean(result['Y']),
            'Y_std': np.std(result['Y']),
            'fs_mean': np.mean(result['fs']),
            'fs_std': np.std(result['fs']),
            'sample_size': result['sample_size']
        })
    summary_df = pd.DataFrame(summary_data)
    print("\n=== Summary Statistics Table ===")
    print(summary_df.to_string(index=False))
    
    # 保存汇总统计到CSV文件
    summary_df.to_csv('r_traversal_S1_V1_kde_summary.csv', index=False)
    print("\nSummary statistics saved to 'r_traversal_S1_V1_kde_summary.csv'")

if __name__ == "__main__":
    main() 