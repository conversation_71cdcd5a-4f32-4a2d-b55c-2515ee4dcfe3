pulse_cols = [
        'PULSE_280V_Z1_LOW', 'EPI_280V_Y1_LOW', 'EPI_700V_Z1_HIGH', 'PULSE_280V_Y2_HIGH',
        'EPI_280V_Z2_LOW', 'PULSE_280V_Y1_HIGH', 'EPI_700V_X2_LOW', 'PULSE_280V_X2_HIGH',
        'PULSE_280V_X1_LOW', 'PULSE_280V_Y1_LOW', 'EPI_280V_Z2_HIGH', 'EPI_700V_Z1_LOW',
        'EPI_700V_X1_LOW', 'PULSE_700V_Y2_LOW', 'Y_STATUS_280V_Y1_High', 'EPI_280V_Z1_HIGH',
        'EPI_280V_X2_LOW', 'X_STATUS_280V_Y1_High', 'PULSE_700V_X2_HIGH', 'EPI_280V_X1_LOW',
        'PULSE_700V_Y1_HIGH', 'EPI_280V_Y1_HIGH', 'PULSE_700V_Z1_LOW', 'EPI_700V_Y1_LOW',
        'EPI_280V_Y2_LOW', 'EPI_700V_X1_HIGH', 'PULSE_700V_X2_LOW', 'PULSE_280V_Z1_HIGH',
        'EPI_280V_Y2_HIGH', 'EPI_700V_X2_HIGH', 'EPI_280V_Z1_LOW', 'PULSE_280V_X2_LOW',
        'PULSE_280V_Z2_HIGH', 'PULSE_280V_Z2_LOW', 'PULSE_700V_Z2_LOW', 'PULSE_700V_X1_HIGH',
        'EPI_280V_X2_HIGH', 'EPI_700V_Y2_HIGH', 'PULSE_280V_Y2_LOW', 'EPI_700V_Y1_HIGH',
        'PULSE_700V_Y1_LOW', 'EPI_700V_Z2_HIGH', 'EPI_700V_Y2_LOW', 'Z_STATUS_280V_Y1_High',
        'EPI_700V_Z2_LOW', 'PULSE_700V_Z1_HIGH', 'PULSE_700V_Y2_HIGH', 'PULSE_280V_X1_HIGH',
        'EPI_280V_X1_HIGH', 'PULSE_700V_X1_LOW', 'PULSE_700V_Z2_HIGH'
    ]
power_on_cols_A = [
    'HST_PRE_TEMP', 'HST_POST_TEMP', 'ICC_TEMP(T0)'
]
power_on_cols_B = [
    'X_CLOSED_LOOP_V280_L_X', 'OUTPUT_VOLTAGE_DISCH_V700_H_Z', '100V_V700_H_Y', '420V_V700_H_X',
    '200V_V280_H_Z', '300V_V280_H_Z', 'X_CLOSED_LOOP_V700_L_Z', 'OUTPUT_VOLTAGE_DISCH_V280_L_Y',
    'HEATSINK_TEMP_VALUE', '200V_V280_L_X', 'X_CLOSED_LOOP_V700_H_Y', '300V_V700_L_Y',
    '300V_V700_H_X', '100V_V700_L_Y', '200V_V700_H_X', '200V_V700_H_Z', '100V_V700_L_Z',
    'BUS_VOLTAGE_VX_LOW', 'Z_CLOSED_LOOP_V280_L_X', '100V_V280_H_Z', '200V_V700_L_X',
    'OUTPUT_VOLTAGE_DISCH_V280_H_Z', 'Z_CLOSED_LOOP_V280_L_Z', '300V_V280_L_Z', '420V_V280_L_Y',
    '200V_V280_H_X', 'Y_CLOSED_LOOP_V700_L_Z', '100V_V280_L_Z', '100V_V700_H_X',
    'Y_CLOSED_LOOP_V280_L_X', 'BUS_VOLTAGE_VBUS', 'X_CLOSED_LOOP_V700_L_Y',
    'OUTPUT_VOLTAGE_DISCH_V700_H_X', '300V_V700_L_Z', 'OUTPUT_VOLTAGE_DISCH_V700_H_Y',
    '100V_V700_L_X', '300V_V280_H_Y', 'Z_CLOSED_LOOP_V700_H_X',
    '420V_V700_L_Z', '300V_V280_L_X', 'OUTPUT_VOLTAGE_DISCH_V280_L_Z', 'Y_CLOSED_LOOP_V700_H_Y',
    '100V_V280_L_X', 'OUTPUT_VOLTAGE_DISCH_V700_L_X', '100V_V280_H_Y', 'Y_CLOSED_LOOP_V280_H_Y',
    '200V_V280_L_Y', 'Y_CLOSED_LOOP_V280_H_Z', '200V_V700_L_Y', 'Y_CLOSED_LOOP_V700_H_X',
    'Z_CLOSED_LOOP_V700_H_Z', '200V_V700_L_Z', '420V_V700_L_Y',
    'Y_CLOSED_LOOP_V280_H_X', 'X_CLOSED_LOOP_V280_H_X',
    'OUTPUT_VOLTAGE_DISCH_V280_L_X', '100V_V700_H_Z', 'Y_CLOSED_LOOP_V280_L_Y',
    'Y_CLOSED_LOOP_V700_H_Z', 'X_CLOSED_LOOP_V280_H_Z', 'Z_CLOSED_LOOP_V700_L_X',
    '420V_V280_L_Z', '100V_V280_H_X', '100V_V280_L_Y', 'Z_CLOSED_LOOP_V280_L_Y',
    'X_CLOSED_LOOP_V280_L_Z', 'BUS_VOLTAGE_VY_LOW', 'OUTPUT_VOLTAGE_DISCH_V700_L_Z',
    'OUTPUT_VOLTAGE_DISCH_V280_H_X', 'Y_CLOSED_LOOP_V700_L_X',
    'OUTPUT_VOLTAGE_DISCH_V700_L_Y', '420V_V700_L_X', 'Z_CLOSED_LOOP_V280_H_Y',
    '200V_V700_H_Y', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y', '420V_V700_H_Y', '300V_V280_L_Y',
    '420V_V280_H_Z', '420V_V280_L_X', '420V_V700_H_Z', 'Z_CLOSED_LOOP_V700_L_Z',
    '300V_V700_H_Z', 'Z_CLOSED_LOOP_V280_H_X', 'X_CLOSED_LOOP_V700_H_X',
    'Y_CLOSED_LOOP_V280_L_Z', 'Z_CLOSED_LOOP_V280_H_Z', 'BUS_VOLTAGE_VZ_LOW',
    'X_CLOSED_LOOP_V700_H_Z', '300V_V280_H_X', 'Z_CLOSED_LOOP_V700_L_Y',
    'Y_CLOSED_LOOP_V700_L_Y', 'X_CLOSED_LOOP_V280_L_Y', 'X_CLOSED_LOOP_V280_H_Y',
    'X_CLOSED_LOOP_V700_L_X', '200V_V280_H_Y', '300V_V700_H_Y',
    '420V_V280_H_Y', '300V_V700_L_X', '420V_V280_H_X', 'Z_CLOSED_LOOP_V700_H_Y',
    '200V_V280_L_Z'
]
burnin_cols = [
    'Heat_Sink_Temp_PS_Lavaflex', 'Heat_Sink_Temp_Amplifier_Lavaflex',
    'Heat_Sink_Temp_Amplifier_Initial', 'Heat_Sink_Temp_Delta_T_Amplifier_Lavaflex',
    'Heat_Sink_Temp_PS_Initial', 'Heat_Sink_Temp_Delta_T_Power_Supply_Lavaflex',
    'Total_Burn_Time'
]
pretest_cols = [
    '328A_IERROR_RMS', '100A_IERROR_Peak', 'EPI_IERROR_RMS', 'Offset_cal',
    '328A_IERROR_Peak', 'Hysteresis', 'Absolute_Error', 'IERROR_Peak_Single_Axis',
    'EPI_Asymmetry', 'Echo_Echo_Stab', '100A_Flattop_Negative', '328A_Flattop_Positive',
    'Ambient_HST_delta', '100A_IERROR_RMS',
    '328A_Flattop_Negative', '100A_Flattop_Positive', 'IERROR_Peak_EPI',
    'Shot_Shot_Stab', 'EPI_IERROR_Peak', 'IERROR_RMS_EPI', 'Ambient_temp', '0A_Flattop'
]