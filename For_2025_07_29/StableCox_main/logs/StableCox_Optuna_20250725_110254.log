2025-07-25 11:02:54 [INFO] StableCox Optuna Optimization Started
2025-07-25 11:02:54 [INFO] Log file: logs\StableCox_Optuna_20250725_110254.log
2025-07-25 11:02:54 [INFO] MPI Configuration: 10 processes
2025-07-25 11:02:54 [INFO] Parameters: {'PATH': 'processed_equipment_data_new.csv', 'seed': 9, 'n_splits': 10, 'reweighting': 'none', 'decorrelation_type': 'global', 'iters_balance': 2500, 'backend': 'Weibull', 'paradigm': 'regr', 'penalizer': 0.03, 'penalizer2': 0.03, 'topN': 5, 'times': 10, 'result_dir': 'results', 'n_trials': 50, 'test_size': 0.1}
2025-07-25 11:02:54 [INFO] Model using in this file is: Weibull
2025-07-25 11:02:54 [INFO] Successfully loaded data from processed_equipment_data_new.csv. Shape: (598, 187)
2025-07-25 11:02:54 [INFO] Data scaler splite with random state 9
2025-07-25 11:02:54 [INFO] Starting Round 1/10
2025-07-25 11:02:54 [INFO] --- Starting Round 0 ---
2025-07-25 11:02:54 [INFO] Data split - Train/Val: (538, 182), Test: (60, 182)
2025-07-25 11:02:54 [INFO] === Stage 1: Starting hyperparameter optimization ===
2025-07-25 11:02:54 [INFO] Use 50 Optuna trials
2025-07-25 11:03:18 [WARNING] Fold 3 failed: NaNs detected in inputs, please correct or drop.
2025-07-25 11:04:02 [WARNING] Fold 5 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 2.0297690045454853
      x: [-5.601e-02 -4.860e-01 ... -1.295e-02  6.332e-01]
    nit: 200
    jac: [-2.348e-04 -5.313e-05 ...  1.757e-05  8.205e-04]
   nfev: 263
   njev: 200

2025-07-25 11:04:14 [WARNING] Fold 0 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 2.020219482419002
      x: [-5.695e-02 -1.152e+00 ...  8.918e-02  6.727e-01]
    nit: 200
    jac: [-1.393e-04  1.172e-04 ... -4.253e-04  4.465e-04]
   nfev: 256
   njev: 200

2025-07-25 11:05:01 [WARNING] Fold 9 failed: NaNs detected in inputs, please correct or drop.
2025-07-25 11:05:03 [WARNING] Fold 0 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 3.5085993649353373
      x: [-9.482e-01 -2.278e-02 ... -2.898e-01 -2.942e+00]
    nit: 200
    jac: [-1.268e-06  2.380e-06 ...  1.335e-06  3.624e-08]
   nfev: 236
   njev: 200

2025-07-25 11:05:11 [WARNING] Fold 1 failed: 
Fitting did not converge. Try the following:

0. Are there any lifelines warnings outputted during the `fit`?
1. Inspect your DataFrame: does everything look as expected?
2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

minimum_results=None

2025-07-25 11:05:13 [WARNING] Fold 2 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 3.508834119166678
      x: [-8.514e-02  9.447e-01 ... -3.393e-01 -2.934e+00]
    nit: 200
    jac: [ 5.328e-07  9.327e-07 ...  3.492e-06  3.691e-08]
   nfev: 243
   njev: 200

2025-07-25 11:05:20 [WARNING] Fold 3 failed: 
Fitting did not converge. Try the following:

0. Are there any lifelines warnings outputted during the `fit`?
1. Inspect your DataFrame: does everything look as expected?
2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

minimum_results=None

2025-07-25 11:05:22 [WARNING] Fold 4 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 1.9377327740755332
      x: [ 8.611e-02 -9.710e-01 ... -1.332e-01  9.386e-01]
    nit: 200
    jac: [ 3.123e-04  5.994e-04 ... -6.073e-04 -1.560e-03]
   nfev: 230
   njev: 200

2025-07-25 11:05:24 [WARNING] Fold 5 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 3.4987507236768782
      x: [ 2.201e-02  4.350e-01 ... -2.946e-01 -2.934e+00]
    nit: 200
    jac: [-2.779e-06  2.006e-06 ...  1.806e-06 -2.708e-08]
   nfev: 233
   njev: 200

2025-07-25 11:05:26 [WARNING] Fold 6 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 3.5019995621174886
      x: [-1.020e+00  4.496e-01 ... -1.131e-01 -2.937e+00]
    nit: 200
    jac: [-8.912e-06  8.303e-06 ... -7.683e-07 -2.642e-07]
   nfev: 243
   njev: 200

2025-07-25 11:05:28 [WARNING] Fold 7 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 3.5023046246846685
      x: [-2.797e-03  1.108e+00 ...  4.577e-02 -2.938e+00]
    nit: 200
    jac: [ 5.018e-06 -5.558e-06 ...  1.473e-06 -1.816e-06]
   nfev: 251
   njev: 200

2025-07-25 11:05:30 [WARNING] Fold 8 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 3.515459949702422
      x: [-3.759e-01 -1.989e-02 ... -3.406e-01 -2.928e+00]
    nit: 200
    jac: [-3.421e-07 -2.047e-07 ... -3.806e-06 -3.317e-08]
   nfev: 239
   njev: 200

2025-07-25 11:05:31 [WARNING] Fold 9 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 3.52531866748456
      x: [ 2.880e-02  1.017e-01 ...  4.185e-01 -2.929e+00]
    nit: 200
    jac: [ 1.755e-06 -1.475e-07 ...  5.341e-06  4.349e-08]
   nfev: 256
   njev: 200

2025-07-25 11:05:33 [WARNING] Fold 0 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 3.503897056080166
      x: [-1.426e+00  1.331e-01 ...  1.548e-01 -2.937e+00]
    nit: 200
    jac: [ 1.041e-06 -5.208e-06 ... -3.612e-06 -2.670e-07]
   nfev: 235
   njev: 200

2025-07-25 11:05:35 [WARNING] Fold 1 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 1.9666532456798473
      x: [ 4.982e-01 -2.975e-01 ... -2.658e-01  7.953e-01]
    nit: 200
    jac: [ 4.025e-04  1.172e-03 ...  1.779e-03 -1.477e-03]
   nfev: 246
   njev: 200

2025-07-25 11:05:37 [WARNING] Fold 2 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 3.5098494859584175
      x: [-3.644e-02  1.906e+00 ... -5.000e-01 -2.939e+00]
    nit: 200
    jac: [-9.105e-06  1.540e-06 ...  7.307e-06  1.118e-06]
   nfev: 242
   njev: 200

2025-07-25 11:05:39 [WARNING] Fold 3 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 3.489784499699509
      x: [-2.268e-01  1.131e+00 ... -4.537e-01 -2.931e+00]
    nit: 200
    jac: [-7.775e-06  8.750e-06 ...  1.714e-05 -3.612e-07]
   nfev: 243
   njev: 200

2025-07-25 11:05:47 [WARNING] Fold 4 failed: 
Fitting did not converge. Try the following:

0. Are there any lifelines warnings outputted during the `fit`?
1. Inspect your DataFrame: does everything look as expected?
2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

minimum_results=None

2025-07-25 11:05:49 [WARNING] Fold 5 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 3.495935951996189
      x: [ 3.142e-01  5.778e-01 ... -1.582e-01 -2.938e+00]
    nit: 200
    jac: [-5.058e-06 -6.216e-07 ...  1.421e-07 -1.021e-07]
   nfev: 243
   njev: 200

2025-07-25 11:05:57 [WARNING] Fold 6 failed: 
Fitting did not converge. Try the following:

0. Are there any lifelines warnings outputted during the `fit`?
1. Inspect your DataFrame: does everything look as expected?
2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

minimum_results=None

2025-07-25 11:05:59 [WARNING] Fold 7 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 3.5027864933869894
      x: [ 5.527e-01  2.073e-01 ...  1.564e-01 -2.940e+00]
    nit: 200
    jac: [ 1.853e-05 -6.561e-06 ...  5.775e-06 -1.992e-07]
   nfev: 231
   njev: 200

2025-07-25 11:06:00 [WARNING] Fold 8 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 3.5054263578603964
      x: [-6.938e-01  4.712e-02 ... -5.862e-01 -2.923e+00]
    nit: 200
    jac: [ 2.168e-06 -3.278e-06 ... -5.630e-06 -1.263e-07]
   nfev: 226
   njev: 200

2025-07-25 11:06:02 [WARNING] Fold 9 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 3.51200949262241
      x: [ 1.783e-01  5.322e-01 ...  2.459e-01 -2.928e+00]
    nit: 200
    jac: [ 8.918e-06 -7.875e-06 ...  1.278e-05  8.263e-07]
   nfev: 235
   njev: 200

2025-07-25 11:06:10 [WARNING] Fold 3 failed: NaNs detected in inputs, please correct or drop.
2025-07-25 11:06:26 [WARNING] Fold 1 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 3.5037570516168626
      x: [ 4.181e-01  6.216e-01 ... -5.390e-02 -2.931e+00]
    nit: 200
    jac: [ 5.571e-07 -1.240e-06 ...  8.069e-07 -3.566e-08]
   nfev: 246
   njev: 200

2025-07-25 11:06:30 [WARNING] Fold 3 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 1.9805834571058711
      x: [-4.145e-02 -1.450e+00 ... -3.970e-01  8.448e-01]
    nit: 200
    jac: [-3.800e-04  6.981e-04 ... -7.232e-04 -1.226e-03]
   nfev: 254
   njev: 200

2025-07-25 11:06:32 [WARNING] Fold 4 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 3.511239813482878
      x: [-1.502e-01  3.712e-01 ... -3.695e-01 -2.936e+00]
    nit: 200
    jac: [ 2.246e-07 -6.143e-07 ... -3.452e-07  1.867e-07]
   nfev: 249
   njev: 200

2025-07-25 11:06:33 [WARNING] Fold 5 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 3.504511163523392
      x: [-1.078e-01  3.877e-01 ... -2.534e-01 -2.936e+00]
    nit: 200
    jac: [-2.658e-06  1.129e-06 ... -1.201e-06  6.088e-08]
   nfev: 250
   njev: 200

2025-07-25 11:06:35 [WARNING] Fold 6 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 3.5181683499077248
      x: [-3.193e-01  1.007e+00 ...  6.080e-02 -2.921e+00]
    nit: 200
    jac: [-3.444e-07 -9.103e-08 ...  1.187e-06  9.118e-07]
   nfev: 248
   njev: 200

2025-07-25 11:06:51 [WARNING] Fold 0 failed: 
Fitting did not converge. Try the following:

0. Are there any lifelines warnings outputted during the `fit`?
1. Inspect your DataFrame: does everything look as expected?
2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

minimum_results=None

2025-07-25 11:07:13 [WARNING] Fold 8 failed: 
Fitting did not converge. Try the following:

0. Are there any lifelines warnings outputted during the `fit`?
1. Inspect your DataFrame: does everything look as expected?
2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

minimum_results=None

2025-07-25 11:07:15 [WARNING] Fold 9 failed: NaNs detected in inputs, please correct or drop.
2025-07-25 11:07:25 [WARNING] Fold 4 failed: NaNs detected in inputs, please correct or drop.
2025-07-25 11:07:36 [WARNING] Fold 6 failed: 
Fitting did not converge. Try the following:

0. Are there any lifelines warnings outputted during the `fit`?
1. Inspect your DataFrame: does everything look as expected?
2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

minimum_results=None

2025-07-25 11:07:46 [WARNING] Fold 8 failed: 
Fitting did not converge. Try the following:

0. Are there any lifelines warnings outputted during the `fit`?
1. Inspect your DataFrame: does everything look as expected?
2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

minimum_results=None

2025-07-25 11:07:52 [WARNING] Fold 1 failed: NaNs detected in inputs, please correct or drop.
2025-07-25 11:08:14 [WARNING] Fold 2 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 2.171965220633085
      x: [ 6.668e-02 -3.812e-01 ...  9.731e-02  2.976e-01]
    nit: 200
    jac: [-6.424e-07 -1.535e-06 ... -2.149e-06  4.768e-06]
   nfev: 262
   njev: 200

2025-07-25 11:08:36 [WARNING] Fold 3 failed: NaNs detected in inputs, please correct or drop.
2025-07-25 11:09:06 [WARNING] Fold 9 failed: NaNs detected in inputs, please correct or drop.
2025-07-25 11:09:08 [WARNING] Fold 0 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 2.1594698847097797
      x: [ 2.990e-01 -4.824e-01 ...  1.417e-01  3.518e-01]
    nit: 200
    jac: [-7.578e-07 -3.579e-07 ...  1.900e-06  9.975e-08]
   nfev: 254
   njev: 200

2025-07-25 11:09:41 [WARNING] Fold 6 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 2.151706404939315
      x: [ 1.927e-01 -4.408e-01 ...  1.388e-01  3.687e-01]
    nit: 200
    jac: [ 8.672e-08 -1.727e-06 ...  2.719e-06 -1.341e-06]
   nfev: 262
   njev: 200

2025-07-25 11:09:55 [WARNING] Fold 3 failed: NaNs detected in inputs, please correct or drop.
2025-07-25 11:10:13 [WARNING] Fold 1 failed: NaNs detected in inputs, please correct or drop.
2025-07-25 11:11:04 [WARNING] Fold 7 failed: NaNs detected in inputs, please correct or drop.
2025-07-25 11:11:24 [WARNING] Fold 5 failed: 
Fitting did not converge. Try the following:

0. Are there any lifelines warnings outputted during the `fit`?
1. Inspect your DataFrame: does everything look as expected?
2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

minimum_results=None

2025-07-25 11:11:29 [WARNING] Fold 8 failed: NaNs detected in inputs, please correct or drop.
2025-07-25 11:11:43 [WARNING] Fold 5 failed: NaNs detected in inputs, please correct or drop.
2025-07-25 11:12:04 [WARNING] Fold 8 failed: NaNs detected in inputs, please correct or drop.
2025-07-25 11:12:15 [WARNING] Fold 0 failed: 
Fitting did not converge. Try the following:

0. Are there any lifelines warnings outputted during the `fit`?
1. Inspect your DataFrame: does everything look as expected?
2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

minimum_results=None

2025-07-25 11:12:25 [WARNING] Fold 1 failed: 
Fitting did not converge. Try the following:

0. Are there any lifelines warnings outputted during the `fit`?
1. Inspect your DataFrame: does everything look as expected?
2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

minimum_results=None

2025-07-25 11:12:32 [WARNING] Fold 5 failed: NaNs detected in inputs, please correct or drop.
2025-07-25 11:12:48 [WARNING] Fold 9 failed: 
Fitting did not converge. Try the following:

0. Are there any lifelines warnings outputted during the `fit`?
1. Inspect your DataFrame: does everything look as expected?
2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

minimum_results=None

2025-07-25 11:13:18 [WARNING] Fold 9 failed: 
Fitting did not converge. Try the following:

0. Are there any lifelines warnings outputted during the `fit`?
1. Inspect your DataFrame: does everything look as expected?
2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

minimum_results=None

2025-07-25 11:13:27 [WARNING] Fold 0 failed: 
Fitting did not converge. Try the following:

0. Are there any lifelines warnings outputted during the `fit`?
1. Inspect your DataFrame: does everything look as expected?
2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

minimum_results=None

2025-07-25 11:13:30 [WARNING] Fold 2 failed: NaNs detected in inputs, please correct or drop.
2025-07-25 11:13:41 [WARNING] Fold 4 failed: 
Fitting did not converge. Try the following:

0. Are there any lifelines warnings outputted during the `fit`?
1. Inspect your DataFrame: does everything look as expected?
2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

minimum_results=None

2025-07-25 11:14:11 [WARNING] Fold 8 failed: NaNs detected in inputs, please correct or drop.
2025-07-25 11:14:19 [WARNING] Fold 2 failed: NaNs detected in inputs, please correct or drop.
2025-07-25 11:14:45 [WARNING] Fold 1 failed: 
Fitting did not converge. Try the following:

0. Are there any lifelines warnings outputted during the `fit`?
1. Inspect your DataFrame: does everything look as expected?
2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

minimum_results=None

2025-07-25 11:14:52 [WARNING] Fold 4 failed: NaNs detected in inputs, please correct or drop.
2025-07-25 11:15:05 [WARNING] Fold 7 failed: 
Fitting did not converge. Try the following:

0. Are there any lifelines warnings outputted during the `fit`?
1. Inspect your DataFrame: does everything look as expected?
2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

minimum_results=None

2025-07-25 11:15:17 [WARNING] Fold 2 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 2.0754863887464645
      x: [-2.975e-01 -2.915e-01 ... -3.787e-02  4.107e-01]
    nit: 200
    jac: [-1.153e-05  4.051e-05 ... -6.306e-05  2.739e-05]
   nfev: 251
   njev: 200

2025-07-25 11:15:47 [WARNING] Fold 5 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 2.061346685881475
      x: [ 1.353e-01 -6.837e-01 ...  1.112e-01  5.276e-01]
    nit: 200
    jac: [ 3.491e-05 -9.169e-06 ... -9.923e-06  3.838e-05]
   nfev: 245
   njev: 200

2025-07-25 11:16:17 [WARNING] Fold 7 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 2.1095454947371306
      x: [ 2.897e-01 -6.339e-01 ...  1.028e-01  3.806e-01]
    nit: 200
    jac: [ 1.538e-06 -9.411e-06 ...  8.464e-06  6.330e-06]
   nfev: 243
   njev: 200

2025-07-25 11:16:46 [WARNING] Fold 2 failed: NaNs detected in inputs, please correct or drop.
2025-07-25 11:17:16 [WARNING] Fold 6 failed: NaNs detected in inputs, please correct or drop.
2025-07-25 11:17:33 [WARNING] Fold 3 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 2.03464019582415
      x: [-7.497e-02 -8.942e-01 ...  1.447e-01  5.489e-01]
    nit: 200
    jac: [-1.087e-04  2.450e-04 ... -4.798e-04 -5.560e-04]
   nfev: 244
   njev: 200

2025-07-25 11:17:47 [WARNING] Fold 9 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 3.511487195621651
      x: [-1.557e-01  6.999e-01 ...  5.729e-01 -2.924e+00]
    nit: 200
    jac: [ 7.328e-08  4.596e-08 ... -5.744e-07  4.710e-08]
   nfev: 253
   njev: 200

2025-07-25 11:17:49 [WARNING] Fold 0 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 3.498809532270495
      x: [-4.174e-01  1.198e+00 ...  2.882e-01 -2.930e+00]
    nit: 200
    jac: [ 7.038e-06  3.636e-06 ... -1.295e-06  7.814e-06]
   nfev: 259
   njev: 200

2025-07-25 11:17:50 [WARNING] Fold 1 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 3.5030106694755734
      x: [ 1.615e-01  1.176e+00 ... -3.783e-01 -2.942e+00]
    nit: 200
    jac: [-1.727e-05  1.005e-05 ... -7.167e-06  1.684e-06]
   nfev: 242
   njev: 200

2025-07-25 11:17:52 [WARNING] Fold 2 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 1.9064451173598673
      x: [-5.061e-01 -1.726e+00 ... -1.398e-01  1.078e+00]
    nit: 200
    jac: [ 3.770e-03 -3.824e-03 ... -1.767e-03  7.049e-04]
   nfev: 255
   njev: 200

2025-07-25 11:17:54 [WARNING] Fold 3 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 3.5057400684080413
      x: [-9.030e-01  1.669e-01 ... -6.445e-01 -2.928e+00]
    nit: 200
    jac: [-6.315e-06 -2.225e-07 ... -1.599e-05 -7.330e-08]
   nfev: 240
   njev: 200

2025-07-25 11:18:02 [WARNING] Fold 4 failed: 
Fitting did not converge. Try the following:

0. Are there any lifelines warnings outputted during the `fit`?
1. Inspect your DataFrame: does everything look as expected?
2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

minimum_results=None

2025-07-25 11:18:04 [WARNING] Fold 5 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 3.4991798435907087
      x: [-4.281e-01  8.669e-01 ... -3.304e-01 -2.935e+00]
    nit: 200
    jac: [-1.157e-05  1.874e-07 ... -3.808e-06 -2.296e-07]
   nfev: 252
   njev: 200

2025-07-25 11:18:06 [WARNING] Fold 6 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 3.4975499864829094
      x: [-4.292e-02  1.983e-01 ...  1.446e-01 -2.938e+00]
    nit: 200
    jac: [-3.833e-07  5.823e-07 ...  3.945e-06 -2.061e-08]
   nfev: 231
   njev: 200

2025-07-25 11:18:08 [WARNING] Fold 7 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 3.490240429057775
      x: [-4.180e-01  1.210e+00 ... -7.471e-01 -2.932e+00]
    nit: 200
    jac: [-2.851e-06  3.647e-06 ... -3.548e-07 -1.771e-07]
   nfev: 243
   njev: 200

2025-07-25 11:18:10 [WARNING] Fold 8 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 3.509566652968415
      x: [-9.631e-01  1.978e-01 ...  5.556e-02 -2.932e+00]
    nit: 200
    jac: [-1.024e-05  1.041e-05 ...  8.654e-06  4.088e-07]
   nfev: 244
   njev: 200

2025-07-25 11:18:12 [WARNING] Fold 9 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 3.5193982990297306
      x: [ 2.484e-01  2.368e-01 ...  4.678e-01 -2.929e+00]
    nit: 200
    jac: [-2.089e-06  1.319e-06 ...  1.715e-06 -2.222e-07]
   nfev: 243
   njev: 200

2025-07-25 11:18:13 [WARNING] Fold 0 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 2.025007135044898
      x: [ 7.273e-02 -8.489e-01 ...  2.237e-02  6.494e-01]
    nit: 200
    jac: [ 8.114e-05  7.797e-05 ...  1.154e-04  3.502e-04]
   nfev: 252
   njev: 200

2025-07-25 11:18:54 [WARNING] Fold 9 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 2.1338877516898065
      x: [-1.314e-01 -6.477e-01 ... -9.425e-02  3.553e-01]
    nit: 200
    jac: [ 1.437e-06  3.270e-06 ... -1.024e-05 -5.771e-06]
   nfev: 240
   njev: 200

2025-07-25 11:19:02 [WARNING] Fold 0 failed: 
Fitting did not converge. Try the following:

0. Are there any lifelines warnings outputted during the `fit`?
1. Inspect your DataFrame: does everything look as expected?
2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

minimum_results=None

2025-07-25 11:19:04 [WARNING] Fold 1 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 3.500090513405034
      x: [-4.053e-01  1.003e+00 ...  2.801e-01 -2.927e+00]
    nit: 200
    jac: [-5.270e-06  1.435e-06 ...  5.237e-06  8.656e-08]
   nfev: 245
   njev: 200

2025-07-25 11:19:13 [WARNING] Fold 2 failed: 
Fitting did not converge. Try the following:

0. Are there any lifelines warnings outputted during the `fit`?
1. Inspect your DataFrame: does everything look as expected?
2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

minimum_results=None

2025-07-25 11:19:15 [WARNING] Fold 3 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 3.5062353981096988
      x: [-4.805e-01  7.866e-01 ... -6.930e-02 -2.933e+00]
    nit: 200
    jac: [-5.348e-06  5.010e-06 ... -5.376e-07  2.029e-07]
   nfev: 238
   njev: 200

2025-07-25 11:19:24 [WARNING] Fold 4 failed: 
Fitting did not converge. Try the following:

0. Are there any lifelines warnings outputted during the `fit`?
1. Inspect your DataFrame: does everything look as expected?
2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

minimum_results=None

2025-07-25 11:19:25 [WARNING] Fold 5 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 3.502438081986555
      x: [ 2.085e-01  8.324e-01 ...  2.921e-01 -2.936e+00]
    nit: 200
    jac: [-3.018e-06 -2.018e-06 ... -9.716e-07 -6.498e-08]
   nfev: 239
   njev: 200

2025-07-25 11:19:27 [WARNING] Fold 6 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 1.9437011114165121
      x: [-9.538e-01  2.209e-01 ... -2.501e-01  9.104e-01]
    nit: 200
    jac: [-2.652e-04  6.178e-04 ...  3.603e-04  2.794e-03]
   nfev: 260
   njev: 200

2025-07-25 11:19:30 [WARNING] Fold 7 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 3.5007763908366742
      x: [-2.281e-02  5.832e-01 ...  1.718e-01 -2.926e+00]
    nit: 200
    jac: [ 5.601e-09 -1.930e-06 ...  7.682e-08  6.754e-08]
   nfev: 243
   njev: 200

2025-07-25 11:19:32 [WARNING] Fold 8 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 3.515275609326353
      x: [ 4.216e-02  6.891e-01 ... -1.168e-01 -2.930e+00]
    nit: 200
    jac: [ 1.987e-07 -1.628e-06 ... -5.370e-06  3.883e-07]
   nfev: 250
   njev: 200

2025-07-25 11:19:33 [WARNING] Fold 9 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 3.5151443229525547
      x: [-8.161e-01 -5.152e-02 ... -2.729e-01 -2.925e+00]
    nit: 200
    jac: [ 2.514e-06  2.485e-06 ... -1.082e-06 -2.455e-08]
   nfev: 256
   njev: 200

2025-07-25 11:19:35 [WARNING] Fold 0 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 3.5114785985464585
      x: [-3.911e-01 -1.846e-01 ... -4.696e-01 -2.946e+00]
    nit: 200
    jac: [-3.988e-05  2.493e-05 ... -1.865e-05  1.253e-06]
   nfev: 250
   njev: 200

2025-07-25 11:19:37 [WARNING] Fold 1 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 3.5070849174398746
      x: [-7.899e-01  1.230e+00 ... -8.924e-02 -2.934e+00]
    nit: 200
    jac: [-4.842e-05  8.702e-06 ... -1.033e-05 -5.574e-07]
   nfev: 265
   njev: 200

2025-07-25 11:19:39 [WARNING] Fold 2 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 3.4970558021722176
      x: [-6.551e-01 -3.538e-01 ...  1.989e-01 -2.934e+00]
    nit: 200
    jac: [-8.466e-06  1.226e-05 ...  6.501e-05 -1.119e-07]
   nfev: 238
   njev: 200

2025-07-25 11:19:40 [WARNING] Fold 3 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 3.4902827867394763
      x: [-1.123e-01 -1.965e-01 ... -9.405e-01 -2.933e+00]
    nit: 200
    jac: [-1.466e-06 -8.936e-06 ...  2.107e-05 -1.865e-07]
   nfev: 227
   njev: 200

2025-07-25 11:19:42 [WARNING] Fold 4 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 1.94855226171722
      x: [-1.930e-01  1.813e-02 ... -1.651e-02  8.033e-01]
    nit: 200
    jac: [-2.591e-03  1.525e-03 ... -3.192e-03 -3.117e-03]
   nfev: 259
   njev: 200

2025-07-25 11:19:43 [WARNING] Fold 5 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 3.486295353887162
      x: [ 2.704e-01  1.473e+00 ... -9.930e-02 -2.927e+00]
    nit: 200
    jac: [-3.989e-06 -7.532e-06 ...  1.615e-05 -1.803e-07]
   nfev: 237
   njev: 200

2025-07-25 11:19:45 [WARNING] Fold 6 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 3.4972315933947966
      x: [-1.223e+00  1.380e+00 ... -2.403e-01 -2.938e+00]
    nit: 200
    jac: [-2.711e-05  1.251e-05 ... -2.299e-06  5.353e-06]
   nfev: 249
   njev: 200

2025-07-25 11:19:46 [WARNING] Fold 7 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 3.4942044064697937
      x: [-3.015e-01  2.181e+00 ...  3.947e-01 -2.923e+00]
    nit: 200
    jac: [-1.115e-06 -6.464e-07 ...  6.602e-06  7.390e-06]
   nfev: 232
   njev: 200

2025-07-25 11:19:48 [WARNING] Fold 8 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 3.5066388449831085
      x: [-1.103e+00  8.657e-01 ... -2.107e-01 -2.939e+00]
    nit: 200
    jac: [ 2.569e-05 -1.411e-05 ...  2.231e-05  1.417e-06]
   nfev: 245
   njev: 200

2025-07-25 11:19:49 [WARNING] Fold 9 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 3.507750167289743
      x: [ 3.942e-02 -8.079e-03 ... -1.332e-01 -2.932e+00]
    nit: 200
    jac: [-1.314e-05  1.161e-05 ... -1.473e-05  3.293e-06]
   nfev: 261
   njev: 200

2025-07-25 11:19:51 [WARNING] Fold 0 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 3.503763786391468
      x: [-7.024e-01  2.587e-01 ... -5.388e-01 -2.928e+00]
    nit: 200
    jac: [-4.292e-07  2.585e-07 ...  2.977e-07  3.315e-08]
   nfev: 242
   njev: 200

2025-07-25 11:19:53 [WARNING] Fold 1 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 3.5059721975638456
      x: [-1.299e-01  6.901e-01 ...  4.083e-01 -2.933e+00]
    nit: 200
    jac: [ 1.603e-06 -1.115e-06 ...  3.089e-06 -9.045e-09]
   nfev: 242
   njev: 200

2025-07-25 11:19:54 [WARNING] Fold 2 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 3.493338928575035
      x: [-3.983e-01  5.522e-01 ...  1.564e-01 -2.923e+00]
    nit: 200
    jac: [ 3.307e-07 -1.229e-06 ... -7.496e-07 -1.869e-08]
   nfev: 244
   njev: 200

2025-07-25 11:19:56 [WARNING] Fold 3 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 3.5017879641009553
      x: [ 1.661e-02  1.184e-01 ... -2.779e-01 -2.931e+00]
    nit: 200
    jac: [ 1.018e-06 -1.154e-07 ...  8.690e-07 -3.020e-08]
   nfev: 248
   njev: 200

2025-07-25 11:19:57 [WARNING] Fold 4 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 1.9915156539627097
      x: [ 4.848e-01 -6.551e-01 ... -8.568e-02  7.453e-01]
    nit: 200
    jac: [-6.343e-04 -1.211e-03 ...  7.911e-04  1.911e-03]
   nfev: 267
   njev: 200

2025-07-25 11:19:59 [WARNING] Fold 5 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 3.499208075054859
      x: [-2.458e-02  6.327e-01 ... -2.274e-02 -2.930e+00]
    nit: 200
    jac: [-1.169e-06  1.039e-06 ...  6.971e-07  4.102e-08]
   nfev: 240
   njev: 200

2025-07-25 11:20:00 [WARNING] Fold 6 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 3.507303470084723
      x: [-1.104e-01  4.138e-01 ... -3.756e-01 -2.932e+00]
    nit: 200
    jac: [-3.407e-07 -4.056e-08 ... -1.384e-06  1.612e-07]
   nfev: 248
   njev: 200

2025-07-25 11:20:01 [WARNING] Fold 7 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 3.505593262431793
      x: [-1.730e-01  2.651e-01 ... -8.594e-02 -2.939e+00]
    nit: 200
    jac: [ 7.558e-07 -1.004e-06 ... -3.701e-07  2.472e-07]
   nfev: 251
   njev: 200

2025-07-25 11:20:03 [WARNING] Fold 8 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 3.516937253648997
      x: [ 2.058e-01  6.922e-01 ... -4.704e-01 -2.925e+00]
    nit: 200
    jac: [ 8.394e-07 -9.498e-07 ... -1.969e-06 -3.023e-08]
   nfev: 241
   njev: 200

2025-07-25 11:20:04 [WARNING] Fold 9 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 3.523637815520258
      x: [-5.654e-01  3.327e-01 ... -2.071e-01 -2.935e+00]
    nit: 200
    jac: [-1.376e-07  3.295e-07 ...  7.294e-07  5.427e-08]
   nfev: 251
   njev: 200

2025-07-25 11:20:13 [WARNING] Fold 5 failed: NaNs detected in inputs, please correct or drop.
2025-07-25 11:20:30 [WARNING] Fold 6 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 2.008134749249393
      x: [ 8.514e-02 -6.702e-01 ...  1.394e-02  7.363e-01]
    nit: 200
    jac: [ 2.966e-04  4.339e-04 ...  6.677e-05  4.431e-04]
   nfev: 249
   njev: 200

2025-07-25 11:20:49 [WARNING] Fold 3 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 2.028305467170344
      x: [-1.247e-02 -6.018e-01 ... -1.370e-01  6.224e-01]
    nit: 200
    jac: [-2.841e-04  4.316e-05 ... -7.888e-06  1.842e-05]
   nfev: 266
   njev: 200

2025-07-25 11:21:16 [WARNING] Fold 9 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 2.132757006591655
      x: [-3.965e-01 -1.632e-01 ... -4.744e-02  3.391e-01]
    nit: 200
    jac: [-3.820e-06 -5.737e-06 ... -3.712e-06 -1.204e-05]
   nfev: 266
   njev: 200

2025-07-25 11:21:33 [WARNING] Fold 9 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `WeibullAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 2.1421452464153616
      x: [-3.536e-01 -2.877e-01 ...  7.702e-02  4.141e-01]
    nit: 200
    jac: [-5.604e-07  1.594e-07 ...  1.842e-06 -4.113e-06]
   nfev: 255
   njev: 200

2025-07-25 11:21:36 [WARNING] Fold 1 failed: NaNs detected in inputs, please correct or drop.
2025-07-25 11:21:49 [INFO] Hyperparameter optimization completed! Best validation C-index: 0.5737
2025-07-25 11:21:49 [INFO] Best parameters: {'penalizer': 0.006680713220915452, 'penalizer2': 1.4102180784675193, 'topN': 7}
2025-07-25 11:21:49 [INFO] === Stage 2: Final model training and evaluation ===
2025-07-25 11:21:49 [INFO] Using optimized weight clipping: min=0.004483, max=2.60
2025-07-25 11:21:49 [INFO] Final model selected 7 features: ['PULSE_280V_Y1_LOW', 'Echo_Echo_Stab', 'Shot_Shot_Stab', 'PULSE_280V_Y2_LOW', 'Y_STATUS_280V_Y1_High', 'HEATSINK_TEMP_VALUE', 'PULSE_700V_X2_LOW']
2025-07-25 11:21:50 [INFO] Final test C-index: 0.5918
2025-07-25 11:21:50 [INFO] Number of selected features: 7
