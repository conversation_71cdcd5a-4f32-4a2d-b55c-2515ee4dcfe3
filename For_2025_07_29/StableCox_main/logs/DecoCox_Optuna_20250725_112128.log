2025-07-25 11:21:28 [INFO] === Decorrelated Cox Analysis with Optuna Optimization ===
2025-07-25 11:21:28 [INFO] Logging to: logs\DecoCox_Optuna_20250725_112128.log
2025-07-25 11:21:28 [INFO] MPI processes: 1
2025-07-25 11:21:28 [INFO] Parameters: {'PATH': 'processed_equipment_data_new.csv', 'seed': 9, 'n_splits': 10, 'n_trials': 40, 'timeout': 300, 'times': 2, 'result_dir': 'results', 'test_size': 0.1}
2025-07-25 11:21:28 [INFO] Successfully loaded data from processed_equipment_data_new.csv. Shape: (598, 187)
2025-07-25 11:21:28 [INFO] Data scaler splite with random state 9
2025-07-25 11:21:28 [INFO] Process 0: Running 2 rounds starting from round 0
2025-07-25 11:21:28 [INFO] Process 0: Starting round 1/2
2025-07-25 11:21:28 [INFO] --- Starting Round 0 ---
2025-07-25 11:21:28 [INFO] Data split - Train/Val: (538, 182), Test: (60, 182)
2025-07-25 11:21:28 [INFO] === Stage 1: Feature selection grid generation ===
2025-07-25 11:21:28 [INFO] === Generating feature selection grid ===
2025-07-25 11:21:28 [INFO] Processing threshold: 0.150
2025-07-25 11:21:28 [INFO] Threshold 0.150: 2 features selected
2025-07-25 11:21:28 [INFO] Processing threshold: 0.200
2025-07-25 11:21:28 [INFO] Threshold 0.200: 2 features selected
2025-07-25 11:21:28 [INFO] Processing threshold: 0.250
2025-07-25 11:21:28 [INFO] Threshold 0.250: 3 features selected
2025-07-25 11:21:28 [INFO] Processing threshold: 0.300
2025-07-25 11:21:28 [INFO] Threshold 0.300: 5 features selected
2025-07-25 11:21:28 [INFO] Processing threshold: 0.350
2025-07-25 11:21:28 [INFO] Threshold 0.350: 8 features selected
2025-07-25 11:21:28 [INFO] Processing threshold: 0.400
2025-07-25 11:21:28 [INFO] Threshold 0.400: 9 features selected
2025-07-25 11:21:28 [INFO] Processing threshold: 0.450
2025-07-25 11:21:29 [INFO] Threshold 0.450: 13 features selected
2025-07-25 11:21:29 [INFO] Generated feature selection grid with 7 threshold settings
2025-07-25 11:21:29 [INFO] === Stage 2: Optuna hyperparameter optimization ===
2025-07-25 11:21:29 [INFO] === Starting Optuna optimization for different feature sets ===
2025-07-25 11:21:29 [INFO] Optimizing with threshold 0.150, 2 features
2025-07-25 11:22:02 [INFO] Threshold 0.150: Best CV C-index = 0.5363
2025-07-25 11:22:02 [INFO] Optimizing with threshold 0.200, 2 features
2025-07-25 11:22:25 [INFO] Threshold 0.200: Best CV C-index = 0.5363
2025-07-25 11:22:25 [INFO] Optimizing with threshold 0.250, 3 features
2025-07-25 11:23:01 [INFO] Threshold 0.250: Best CV C-index = 0.5906
2025-07-25 11:23:01 [INFO] Optimizing with threshold 0.300, 5 features
2025-07-25 11:23:44 [INFO] Threshold 0.300: Best CV C-index = 0.5906
2025-07-25 11:23:44 [INFO] Optimizing with threshold 0.350, 8 features
2025-07-25 11:24:32 [INFO] Threshold 0.350: Best CV C-index = 0.5770
2025-07-25 11:24:32 [INFO] Optimizing with threshold 0.400, 9 features
2025-07-25 11:25:25 [INFO] Threshold 0.400: Best CV C-index = 0.5770
2025-07-25 11:25:25 [INFO] Optimizing with threshold 0.450, 13 features
2025-07-25 11:26:16 [INFO] Threshold 0.450: Best CV C-index = 0.5722
2025-07-25 11:26:16 [INFO] === Stage 3: Selecting best feature set and hyperparameters ===
2025-07-25 11:26:16 [INFO] Best feature set: threshold=0.250, num_features=3, Feature names=['HST_PRE_TEMP', 'Heat_Sink_Temp_Amplifier_Lavaflex', 'Total_Burn_Time'], cv_score=0.5906
2025-07-25 11:26:16 [INFO] === Stage 4: Final model training and evaluation ===
2025-07-25 11:26:16 [INFO] === Training final model and evaluation ===
2025-07-25 11:26:16 [INFO] Final test C-index: 0.6264
2025-07-25 11:26:16 [INFO] Round 1 completed successfully
2025-07-25 11:26:16 [INFO] Process 0: Starting round 2/2
2025-07-25 11:26:16 [INFO] --- Starting Round 1 ---
2025-07-25 11:26:16 [INFO] Data split - Train/Val: (538, 182), Test: (60, 182)
2025-07-25 11:26:16 [INFO] === Stage 1: Feature selection grid generation ===
2025-07-25 11:26:16 [INFO] === Generating feature selection grid ===
2025-07-25 11:26:16 [INFO] Processing threshold: 0.150
2025-07-25 11:26:16 [INFO] Threshold 0.150: 2 features selected
2025-07-25 11:26:16 [INFO] Processing threshold: 0.200
2025-07-25 11:26:16 [INFO] Threshold 0.200: 2 features selected
2025-07-25 11:26:16 [INFO] Processing threshold: 0.250
2025-07-25 11:26:16 [INFO] Threshold 0.250: 3 features selected
2025-07-25 11:26:16 [INFO] Processing threshold: 0.300
2025-07-25 11:26:16 [INFO] Threshold 0.300: 5 features selected
2025-07-25 11:26:16 [INFO] Processing threshold: 0.350
2025-07-25 11:26:16 [INFO] Threshold 0.350: 8 features selected
2025-07-25 11:26:16 [INFO] Processing threshold: 0.400
2025-07-25 11:26:16 [INFO] Threshold 0.400: 9 features selected
2025-07-25 11:26:16 [INFO] Processing threshold: 0.450
2025-07-25 11:26:16 [INFO] Threshold 0.450: 13 features selected
2025-07-25 11:26:16 [INFO] Generated feature selection grid with 7 threshold settings
2025-07-25 11:26:16 [INFO] === Stage 2: Optuna hyperparameter optimization ===
2025-07-25 11:26:16 [INFO] === Starting Optuna optimization for different feature sets ===
2025-07-25 11:26:16 [INFO] Optimizing with threshold 0.150, 2 features
2025-07-25 11:26:30 [INFO] Threshold 0.150: Best CV C-index = 0.5282
2025-07-25 11:26:30 [INFO] Optimizing with threshold 0.200, 2 features
2025-07-25 11:26:44 [INFO] Threshold 0.200: Best CV C-index = 0.5282
2025-07-25 11:26:44 [INFO] Optimizing with threshold 0.250, 3 features
2025-07-25 11:27:20 [INFO] Threshold 0.250: Best CV C-index = 0.5839
2025-07-25 11:27:20 [INFO] Optimizing with threshold 0.300, 5 features
2025-07-25 11:28:07 [INFO] Threshold 0.300: Best CV C-index = 0.5885
2025-07-25 11:28:07 [INFO] Optimizing with threshold 0.350, 8 features
2025-07-25 11:28:59 [INFO] Threshold 0.350: Best CV C-index = 0.5594
2025-07-25 11:28:59 [INFO] Optimizing with threshold 0.400, 9 features
2025-07-25 11:30:27 [INFO] Threshold 0.400: Best CV C-index = 0.5737
2025-07-25 11:30:27 [INFO] Optimizing with threshold 0.450, 13 features
2025-07-25 11:32:24 [INFO] Threshold 0.450: Best CV C-index = 0.5674
2025-07-25 11:32:24 [INFO] === Stage 3: Selecting best feature set and hyperparameters ===
2025-07-25 11:32:24 [INFO] Best feature set: threshold=0.300, num_features=5, Feature names=['HST_PRE_TEMP', 'Heat_Sink_Temp_Amplifier_Lavaflex', 'Heat_Sink_Temp_Delta_T_Power_Supply_Lavaflex', 'Heat_Sink_Temp_PS_Lavaflex', 'Total_Burn_Time'], cv_score=0.5885
2025-07-25 11:32:24 [INFO] === Stage 4: Final model training and evaluation ===
2025-07-25 11:32:24 [INFO] === Training final model and evaluation ===
2025-07-25 11:32:25 [INFO] Final test C-index: 0.6264
2025-07-25 11:32:25 [INFO] Round 2 completed successfully
