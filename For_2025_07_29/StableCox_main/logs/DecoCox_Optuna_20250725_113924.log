2025-07-25 11:39:24 [INFO] === Decorrelated Cox Analysis with Optuna Optimization ===
2025-07-25 11:39:24 [INFO] Logging to: logs\DecoCox_Optuna_20250725_113924.log
2025-07-25 11:39:24 [INFO] MPI processes: 1
2025-07-25 11:39:24 [INFO] Parameters: {'PATH': 'processed_equipment_data_new.csv', 'seed': 9, 'n_splits': 10, 'n_trials': 40, 'timeout': 300, 'times': 2, 'result_dir': 'results', 'test_size': 0.1}
2025-07-25 11:39:24 [INFO] Successfully loaded data from processed_equipment_data_new.csv. Shape: (598, 187)
2025-07-25 11:39:24 [INFO] Data scaler splite with random state 9
2025-07-25 11:39:24 [INFO] Process 0: Running 2 rounds starting from round 0
2025-07-25 11:39:24 [INFO] Process 0: Starting round 1/2
2025-07-25 11:39:24 [INFO] --- Starting Round 0 ---
2025-07-25 11:39:24 [INFO] Data split - Train/Val: (538, 182), Test: (60, 182)
2025-07-25 11:39:24 [INFO] === Stage 1: Feature selection grid generation ===
2025-07-25 11:39:24 [INFO] === Generating feature selection grid ===
2025-07-25 11:39:24 [INFO] Processing threshold: 0.150
2025-07-25 11:39:24 [INFO] Threshold 0.150: 2 features selected
2025-07-25 11:39:24 [INFO] Processing threshold: 0.200
2025-07-25 11:39:24 [INFO] Threshold 0.200: 2 features selected
2025-07-25 11:39:24 [INFO] Processing threshold: 0.250
2025-07-25 11:39:24 [INFO] Threshold 0.250: 3 features selected
2025-07-25 11:39:24 [INFO] Processing threshold: 0.300
2025-07-25 11:39:24 [INFO] Threshold 0.300: 5 features selected
2025-07-25 11:39:24 [INFO] Processing threshold: 0.350
2025-07-25 11:39:24 [INFO] Threshold 0.350: 7 features selected
2025-07-25 11:39:24 [INFO] Processing threshold: 0.400
2025-07-25 11:39:24 [INFO] Threshold 0.400: 10 features selected
2025-07-25 11:39:24 [INFO] Processing threshold: 0.450
2025-07-25 11:39:24 [INFO] Threshold 0.450: 12 features selected
2025-07-25 11:39:24 [INFO] Generated feature selection grid with 7 threshold settings
2025-07-25 11:39:24 [INFO] === Stage 2: Optuna hyperparameter optimization ===
2025-07-25 11:39:24 [INFO] === Starting Optuna optimization for different feature sets ===
2025-07-25 11:39:24 [INFO] Optimizing with threshold 0.150, 2 features
2025-07-25 11:40:06 [INFO] Threshold 0.150: Best CV C-index = 0.5487
2025-07-25 11:40:06 [INFO] Optimizing with threshold 0.200, 2 features
2025-07-25 11:40:46 [INFO] Threshold 0.200: Best CV C-index = 0.5487
2025-07-25 11:40:46 [INFO] Optimizing with threshold 0.250, 3 features
2025-07-25 11:41:35 [INFO] Threshold 0.250: Best CV C-index = 0.6065
2025-07-25 11:41:35 [INFO] Optimizing with threshold 0.300, 5 features
2025-07-25 11:42:37 [INFO] Threshold 0.300: Best CV C-index = 0.5946
2025-07-25 11:42:37 [INFO] Optimizing with threshold 0.350, 7 features
2025-07-25 11:44:16 [INFO] Threshold 0.350: Best CV C-index = 0.5871
2025-07-25 11:44:16 [INFO] Optimizing with threshold 0.400, 10 features
2025-07-25 11:46:05 [INFO] Threshold 0.400: Best CV C-index = 0.5881
2025-07-25 11:46:05 [INFO] Optimizing with threshold 0.450, 12 features
2025-07-25 11:47:51 [INFO] Threshold 0.450: Best CV C-index = 0.5856
2025-07-25 11:47:51 [INFO] === Stage 3: Selecting best feature set and hyperparameters ===
2025-07-25 11:47:51 [INFO] Best feature set: threshold=0.250, num_features=3, Feature names=['HST_POST_TEMP', 'Heat_Sink_Temp_Amplifier_Lavaflex', 'Total_Burn_Time'], cv_score=0.6065
2025-07-25 11:47:51 [INFO] === Stage 4: Final model training and evaluation ===
2025-07-25 11:47:51 [INFO] === Training final model and evaluation ===
2025-07-25 11:47:51 [INFO] Final test C-index: 0.4194
2025-07-25 11:47:51 [INFO] Round 1 completed successfully
2025-07-25 11:47:51 [INFO] Process 0: Starting round 2/2
2025-07-25 11:47:51 [INFO] --- Starting Round 1 ---
2025-07-25 11:47:51 [INFO] Data split - Train/Val: (538, 182), Test: (60, 182)
2025-07-25 11:47:51 [INFO] === Stage 1: Feature selection grid generation ===
2025-07-25 11:47:51 [INFO] === Generating feature selection grid ===
2025-07-25 11:47:51 [INFO] Processing threshold: 0.150
2025-07-25 11:47:51 [INFO] Threshold 0.150: 2 features selected
2025-07-25 11:47:51 [INFO] Processing threshold: 0.200
2025-07-25 11:47:51 [INFO] Threshold 0.200: 2 features selected
2025-07-25 11:47:51 [INFO] Processing threshold: 0.250
2025-07-25 11:47:51 [INFO] Threshold 0.250: 3 features selected
2025-07-25 11:47:51 [INFO] Processing threshold: 0.300
2025-07-25 11:47:51 [INFO] Threshold 0.300: 5 features selected
2025-07-25 11:47:51 [INFO] Processing threshold: 0.350
2025-07-25 11:47:51 [INFO] Threshold 0.350: 7 features selected
2025-07-25 11:47:51 [INFO] Processing threshold: 0.400
2025-07-25 11:47:51 [INFO] Threshold 0.400: 10 features selected
2025-07-25 11:47:51 [INFO] Processing threshold: 0.450
2025-07-25 11:47:51 [INFO] Threshold 0.450: 12 features selected
2025-07-25 11:47:51 [INFO] Generated feature selection grid with 7 threshold settings
2025-07-25 11:47:51 [INFO] === Stage 2: Optuna hyperparameter optimization ===
2025-07-25 11:47:51 [INFO] === Starting Optuna optimization for different feature sets ===
2025-07-25 11:47:51 [INFO] Optimizing with threshold 0.150, 2 features
2025-07-25 11:48:16 [INFO] Threshold 0.150: Best CV C-index = 0.5577
2025-07-25 11:48:16 [INFO] Optimizing with threshold 0.200, 2 features
2025-07-25 11:48:42 [INFO] Threshold 0.200: Best CV C-index = 0.5577
2025-07-25 11:48:42 [INFO] Optimizing with threshold 0.250, 3 features
2025-07-25 11:49:10 [INFO] Threshold 0.250: Best CV C-index = 0.6090
2025-07-25 11:49:10 [INFO] Optimizing with threshold 0.300, 5 features
2025-07-25 11:50:07 [INFO] Threshold 0.300: Best CV C-index = 0.5973
2025-07-25 11:50:07 [INFO] Optimizing with threshold 0.350, 7 features
2025-07-25 11:51:04 [INFO] Threshold 0.350: Best CV C-index = 0.5848
2025-07-25 11:51:04 [INFO] Optimizing with threshold 0.400, 10 features
2025-07-25 11:52:05 [INFO] Threshold 0.400: Best CV C-index = 0.5844
2025-07-25 11:52:05 [INFO] Optimizing with threshold 0.450, 12 features
2025-07-25 11:53:04 [INFO] Threshold 0.450: Best CV C-index = 0.5853
2025-07-25 11:53:04 [INFO] === Stage 3: Selecting best feature set and hyperparameters ===
2025-07-25 11:53:04 [INFO] Best feature set: threshold=0.250, num_features=3, Feature names=['HST_POST_TEMP', 'Heat_Sink_Temp_Amplifier_Lavaflex', 'Total_Burn_Time'], cv_score=0.6090
2025-07-25 11:53:04 [INFO] === Stage 4: Final model training and evaluation ===
2025-07-25 11:53:04 [INFO] === Training final model and evaluation ===
2025-07-25 11:53:04 [INFO] Final test C-index: 0.4194
2025-07-25 11:53:04 [INFO] Round 2 completed successfully
2025-07-25 11:53:04 [INFO] ======================================================================
2025-07-25 11:53:04 [INFO] FINAL RESULTS SUMMARY - 2 Independent Runs
2025-07-25 11:53:04 [INFO] ======================================================================
2025-07-25 11:53:04 [INFO] best_threshold: Mean=0.2500, Std=0.0000, CV=0.00%
2025-07-25 11:53:04 [INFO] best_num_features: Mean=3.0000, Std=0.0000, CV=0.00%
2025-07-25 11:53:04 [INFO] best_cv_score: Mean=0.6077, Std=0.0012, CV=0.20%
2025-07-25 11:53:04 [INFO] final_test_c_index: Mean=0.4194, Std=0.0000, CV=0.00%
2025-07-25 11:53:04 [INFO] --------------------------------------------------
2025-07-25 11:53:04 [INFO] TEST SET PERFORMANCE ANALYSIS
2025-07-25 11:53:04 [INFO] --------------------------------------------------
2025-07-25 11:53:04 [INFO] Test C-indices: ['0.4194', '0.4194']
2025-07-25 11:53:04 [INFO] Final Test Set Performance Summary:
2025-07-25 11:53:04 [INFO] Final Test C-indices: ['0.4194', '0.4194']
2025-07-25 11:53:04 [INFO] Mean Final Test C-index: 0.4194
2025-07-25 11:53:04 [INFO] Best Final Test C-index: 0.4194
2025-07-25 11:53:04 [INFO] Std Dev of Final Test C-index: 0.0000
2025-07-25 11:53:04 [INFO] Worst Final Test C-index: 0.4194
2025-07-25 11:53:04 [INFO] Total execution time: 820.75 seconds
2025-07-25 11:53:04 [INFO] === Execution completed ===
