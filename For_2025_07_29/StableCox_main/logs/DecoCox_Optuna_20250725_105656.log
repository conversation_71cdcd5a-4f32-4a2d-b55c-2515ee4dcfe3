2025-07-25 10:56:56 [INFO] === Decorrelated Cox Analysis with Optuna Optimization ===
2025-07-25 10:56:56 [INFO] Logging to: logs\DecoCox_Optuna_20250725_105656.log
2025-07-25 10:56:56 [INFO] MPI processes: 1
2025-07-25 10:56:56 [INFO] Parameters: {'PATH': 'processed_equipment_data_new.csv', 'seed': 9, 'n_splits': 10, 'n_trials': 40, 'timeout': 300, 'times': 1, 'result_dir': 'results', 'test_size': 0.1}
2025-07-25 10:56:56 [INFO] Successfully loaded data from processed_equipment_data_new.csv. Shape: (598, 187)
2025-07-25 10:56:56 [INFO] Data scaler splite with random state 9
2025-07-25 10:56:56 [INFO] Process 0: Running 1 rounds starting from round 0
2025-07-25 10:56:56 [INFO] Process 0: Starting round 1/1
2025-07-25 10:56:56 [INFO] --- Starting Round 0 ---
2025-07-25 10:56:56 [INFO] Data split - Train/Val: (538, 182), Test: (60, 182)
2025-07-25 10:56:56 [INFO] === Stage 1: Feature selection grid generation ===
2025-07-25 10:56:56 [INFO] === Generating feature selection grid ===
2025-07-25 10:56:56 [INFO] Processing threshold: 0.150
2025-07-25 10:56:56 [INFO] Threshold 0.150: 2 features selected
2025-07-25 10:56:56 [INFO] Processing threshold: 0.200
2025-07-25 10:56:56 [INFO] Threshold 0.200: 2 features selected
2025-07-25 10:56:56 [INFO] Processing threshold: 0.250
2025-07-25 10:56:56 [INFO] Threshold 0.250: 3 features selected
2025-07-25 10:56:56 [INFO] Processing threshold: 0.300
2025-07-25 10:56:56 [INFO] Threshold 0.300: 5 features selected
2025-07-25 10:56:56 [INFO] Processing threshold: 0.350
2025-07-25 10:56:56 [INFO] Threshold 0.350: 7 features selected
2025-07-25 10:56:56 [INFO] Processing threshold: 0.400
2025-07-25 10:56:56 [INFO] Threshold 0.400: 9 features selected
2025-07-25 10:56:56 [INFO] Processing threshold: 0.450
2025-07-25 10:56:56 [INFO] Threshold 0.450: 12 features selected
2025-07-25 10:56:56 [INFO] Generated feature selection grid with 7 threshold settings
2025-07-25 10:56:56 [INFO] === Stage 2: Optuna hyperparameter optimization ===
2025-07-25 10:56:56 [INFO] === Starting Optuna optimization for different feature sets ===
2025-07-25 10:56:56 [INFO] Optimizing with threshold 0.150, 2 features
2025-07-25 10:57:17 [INFO] Threshold 0.150: Best CV C-index = 0.5333
2025-07-25 10:57:17 [INFO] Optimizing with threshold 0.200, 2 features
2025-07-25 10:57:41 [INFO] Threshold 0.200: Best CV C-index = 0.5333
2025-07-25 10:57:41 [INFO] Optimizing with threshold 0.250, 3 features
2025-07-25 10:58:07 [INFO] Threshold 0.250: Best CV C-index = 0.5684
2025-07-25 10:58:07 [INFO] Optimizing with threshold 0.300, 5 features
2025-07-25 10:58:32 [INFO] Threshold 0.300: Best CV C-index = 0.5452
2025-07-25 10:58:32 [INFO] Optimizing with threshold 0.350, 7 features
2025-07-25 10:59:23 [INFO] Threshold 0.350: Best CV C-index = 0.5431
2025-07-25 10:59:23 [INFO] Optimizing with threshold 0.400, 9 features
2025-07-25 10:59:53 [INFO] Threshold 0.400: Best CV C-index = 0.5287
2025-07-25 10:59:53 [INFO] Optimizing with threshold 0.450, 12 features
2025-07-25 11:00:17 [INFO] Threshold 0.450: Best CV C-index = 0.5357
2025-07-25 11:00:17 [INFO] === Stage 3: Selecting best feature set and hyperparameters ===
2025-07-25 11:00:17 [INFO] Best feature set: threshold=0.250, num_features=3, cv_score=0.5684
2025-07-25 11:00:17 [INFO] === Stage 4: Final model training and evaluation ===
2025-07-25 11:00:17 [INFO] === Training final model and evaluation ===
2025-07-25 11:00:17 [INFO] Final test C-index: 0.6538
2025-07-25 11:00:17 [INFO] Round 1 completed successfully
