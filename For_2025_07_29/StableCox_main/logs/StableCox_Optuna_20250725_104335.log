2025-07-25 10:43:35 [INFO] StableCox Optuna Optimization Started
2025-07-25 10:43:35 [INFO] Log file: logs\StableCox_Optuna_20250725_104335.log
2025-07-25 10:43:35 [INFO] MPI Configuration: 10 processes
2025-07-25 10:43:35 [INFO] Parameters: {'PATH': 'processed_equipment_data_new.csv', 'seed': 9, 'n_splits': 10, 'reweighting': 'none', 'decorrelation_type': 'global', 'iters_balance': 2500, 'backend': 'LogLogistic', 'paradigm': 'regr', 'penalizer': 0.03, 'penalizer2': 0.03, 'topN': 5, 'times': 10, 'result_dir': 'results', 'n_trials': 50, 'test_size': 0.1}
2025-07-25 10:43:35 [INFO] Model using in this file is: LogLogistic
2025-07-25 10:43:35 [INFO] Successfully loaded data from processed_equipment_data_new.csv. Shape: (598, 187)
2025-07-25 10:43:35 [INFO] Data scaler splite with random state 9
2025-07-25 10:43:35 [INFO] Starting Round 1/10
2025-07-25 10:43:35 [INFO] --- Starting Round 0 ---
2025-07-25 10:43:35 [INFO] Data split - Train/Val: (538, 182), Test: (60, 182)
2025-07-25 10:43:35 [INFO] === Stage 1: Starting hyperparameter optimization ===
2025-07-25 10:43:35 [INFO] Use 50 Optuna trials
2025-07-25 10:44:16 [WARNING] Fold 3 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `LogLogisticAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 1.8888495902830689
      x: [ 4.314e-01 -6.406e-01 ... -4.411e-01  1.395e+00]
    nit: 200
    jac: [-7.025e-04 -6.124e-04 ... -1.486e-03  9.194e-04]
   nfev: 207
   njev: 200

2025-07-25 10:46:02 [WARNING] Fold 4 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `LogLogisticAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 1.9005374696686401
      x: [ 1.664e-01 -8.881e-01 ... -1.195e-01  1.323e+00]
    nit: 200
    jac: [-1.261e-03 -2.848e-04 ... -2.543e-05  1.363e-03]
   nfev: 206
   njev: 200

2025-07-25 10:46:43 [WARNING] Fold 3 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `LogLogisticAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 1.9382038078351331
      x: [ 2.565e-01 -1.339e+00 ... -6.152e-02  1.172e+00]
    nit: 200
    jac: [-1.219e-04  6.162e-05 ... -1.220e-04  1.097e-04]
   nfev: 206
   njev: 200

2025-07-25 10:52:14 [WARNING] Fold 5 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `LogLogisticAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 1.98459636175478
      x: [ 2.289e-01 -6.619e-01 ... -5.866e-02  1.027e+00]
    nit: 200
    jac: [-5.194e-05 -1.871e-05 ... -3.210e-05 -6.155e-05]
   nfev: 206
   njev: 200

2025-07-25 10:52:42 [WARNING] Fold 8 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `LogLogisticAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 1.9642953436675297
      x: [ 5.442e-01 -1.219e+00 ...  1.347e-02  1.173e+00]
    nit: 200
    jac: [-1.568e-05  1.251e-04 ...  1.733e-04 -2.156e-04]
   nfev: 208
   njev: 200

2025-07-25 10:55:43 [WARNING] Fold 5 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `LogLogisticAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 1.9301300119245162
      x: [ 8.640e-01 -1.230e+00 ... -7.144e-03  1.208e+00]
    nit: 200
    jac: [ 7.688e-04  5.967e-04 ...  4.592e-04 -1.286e-03]
   nfev: 206
   njev: 200

2025-07-25 10:56:27 [WARNING] Fold 4 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `LogLogisticAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 1.9212614040456035
      x: [ 6.432e-01 -1.331e+00 ... -7.246e-02  1.329e+00]
    nit: 200
    jac: [ 2.306e-04 -1.162e-03 ... -6.942e-04 -1.281e-03]
   nfev: 208
   njev: 200

2025-07-25 11:00:38 [WARNING] Fold 1 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `LogLogisticAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 1.953709020613982
      x: [-5.529e-02 -1.370e+00 ... -1.978e-01  1.077e+00]
    nit: 200
    jac: [ 1.506e-05  5.783e-04 ...  2.696e-04 -7.039e-04]
   nfev: 208
   njev: 200

2025-07-25 11:00:46 [INFO] Hyperparameter optimization completed! Best validation C-index: 0.5827
2025-07-25 11:00:46 [INFO] Best parameters: {'penalizer': 0.0005297051786886187, 'penalizer2': 0.6879618099189012, 'topN': 7}
2025-07-25 11:00:46 [INFO] === Stage 2: Final model training and evaluation ===
2025-07-25 11:00:46 [INFO] Using optimized weight clipping: min=0.004483, max=2.60
2025-07-25 11:00:47 [INFO] Final model selected 7 features: ['Echo_Echo_Stab', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'Y_STATUS_280V_Y1_High', 'Shot_Shot_Stab', 'HEATSINK_TEMP_VALUE', 'PULSE_700V_X2_LOW', 'PULSE_280V_Z2_HIGH']
2025-07-25 11:00:47 [INFO] Final test C-index: 0.6020
2025-07-25 11:00:47 [INFO] Number of selected features: 7
2025-07-25 11:01:29 [INFO] ======================================================================
2025-07-25 11:01:29 [INFO] FINAL RESULTS SUMMARY - 10 Independent Runs
2025-07-25 11:01:29 [INFO] ======================================================================
2025-07-25 11:01:29 [INFO] best_cv_score: Mean=0.5698, Std=0.0119, CV=2.09%
2025-07-25 11:01:29 [INFO] final_test_c_index: Mean=0.5551, Std=0.0554, CV=9.97%
2025-07-25 11:01:29 [INFO] selected_features_count: Mean=7.1000, Std=1.5133, CV=21.31%
2025-07-25 11:01:29 [INFO] --------------------------------------------------
2025-07-25 11:01:29 [INFO] TEST SET PERFORMANCE ANALYSIS
2025-07-25 11:01:29 [INFO] --------------------------------------------------
2025-07-25 11:01:29 [INFO] Test C-indices: ['0.6020', '0.4694', '0.5918', '0.5102', '0.5510', '0.5918', '0.6020', '0.5816', '0.4490', '0.6020']
2025-07-25 11:01:29 [INFO] Final Test Set Performance Summary:
2025-07-25 11:01:29 [INFO] Final Test C-indices: ['0.6020', '0.4694', '0.5918', '0.5102', '0.5510', '0.5918', '0.6020', '0.5816', '0.4490', '0.6020']
2025-07-25 11:01:29 [INFO] Mean Final Test C-index: 0.5551
2025-07-25 11:01:29 [INFO] Best Final Test C-index: 0.6020
2025-07-25 11:01:29 [INFO] Std Dev of Final Test C-index: 0.0554
2025-07-25 11:01:29 [INFO] Worst Final Test C-index: 0.4490
2025-07-25 11:01:29 [INFO] ==================================================
