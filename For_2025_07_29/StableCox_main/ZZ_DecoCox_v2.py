# 导入必要的库
from data.selection_bias import gen_selection_bias_data
from algorithm.DWR import DWR
from algorithm.SRDO import SRDO
from model.linear import get_algorithm_class
from metrics import get_metric_class
from utils import setup_seed, get_beta_s, get_expname, calc_var, pretty, get_cov_mask, BV_analysis
from Logger import Logger
from model.STG import STG
from sksurv.metrics import concordance_index_censored
from sklearn.metrics import mean_squared_error, f1_score, r2_score
import numpy as np
import os
import torch
from collections import defaultdict as dd
import pandas as pd
from sklearn.preprocessing import StandardScaler
from lifelines import CoxPHFitter
import seaborn as sns
import matplotlib.pyplot as plt
from lifelines.statistics import logrank_test
from lifelines import KaplanMeierFitter
from sksurv.util import Surv
from lifelines.utils import concordance_index
from sklearn.metrics import roc_auc_score, accuracy_score
import logging
import warnings
warnings.filterwarnings('ignore')
from mpi4py import MPI
import time
from sklearn.model_selection import StratifiedKFold
import optuna
from optuna.samplers import TPESampler

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S"
)

duration_col = 'duration'
event_col = 'event'
SN_col = 'SN_Common'


def data_processing(PATH):
    # 1. 加载全数据集 df
    try:
        df = pd.read_csv(PATH)
        logging.info(f"Successfully loaded data from {PATH}. Shape: {df.shape}")
    except FileNotFoundError:
        logging.error(f"Data file not found at {PATH}. Please check path.")
        return {}

    # 数据预处理
    if SN_col in df.columns:
        df = df.drop(columns=[SN_col])
    problematic_cols = ['0A_Flattop', 'Ambient_HST_delta', 'Ambient_temp', 'Offset_cal']
    cols_to_drop = [col for col in problematic_cols if col in df.columns]
    df = df.drop(columns=cols_to_drop)
    feature_cols = [col for col in df.columns if col not in [duration_col, event_col]]

    from sklearn.model_selection import train_test_split

    # 2. 第一次分割：全数据 -> 训练验证集 + 测试集

    df_trainval, df_test = train_test_split(
        df,
        test_size=params['test_size'],
        # random_state=params['seed'],
        stratify=df[event_col]
    )
    logging.info(f'Data scaler splite with random state {params["seed"]}')
    scaler = StandardScaler()
    scaler.fit(df_trainval[feature_cols])
    df_trainval[feature_cols] = scaler.transform(df_trainval[feature_cols])
    df_test[feature_cols] = scaler.transform(df_test[feature_cols])

    return df_trainval, df_test, feature_cols

def remove_highly_correlated_features(df, columns, threshold=0.8, plot=False):
    """
    移除高度相关的特征，并提供详细的分析报告
    注意：这个版本关闭了绘图功能以避免在并行运行时出现问题

    参数:
    df: DataFrame
    columns: 要分析的列名列表
    threshold: 相关性阈值，默认0.8
    plot: 是否显示可视化，默认False

    返回:
    selected_cols: 保留的列名列表
    """
    if len(columns) == 0:
        return []

    # 1. 计算相关性矩阵
    corr_matrix = df[columns].corr().abs()

    # 2. 获取上三角矩阵
    upper_triangle = corr_matrix.where(np.triu(np.ones(corr_matrix.shape), k=1).astype(bool))

    # 3. 找出高相关性的特征对
    high_corr_pairs = []
    for column in upper_triangle.columns:
        high_corr = upper_triangle[column][upper_triangle[column] > threshold]
        for idx, value in high_corr.items():
            high_corr_pairs.append((column, idx, value))

    # 4. 按相关性大小排序
    high_corr_pairs = sorted(high_corr_pairs, key=lambda x: x[2], reverse=True)

    # 5. 确定要移除的列
    high_corr_cols = []
    seen = set()
    for col1, col2, corr in high_corr_pairs:
        if col1 not in seen and col2 not in seen:
            # 根据方差选择保留哪个特征
            var1 = df[col1].var()
            var2 = df[col2].var()
            if var1 >= var2:
                high_corr_cols.append(col2)
                seen.add(col2)
            else:
                high_corr_cols.append(col1)
                seen.add(col1)
        elif col1 not in seen:
            high_corr_cols.append(col1)
            seen.add(col1)
        elif col2 not in seen:
            high_corr_cols.append(col2)
            seen.add(col2)

    # 6. 获取保留的列
    selected_cols = [col for col in columns if col not in high_corr_cols]

    return selected_cols


def generate_feature_selection_grid(df_trainval, all_feature_cols, thresholds):
    """
    在trainval数据上生成不同threshold的特征选择结果
    严格避免数据泄露：只使用trainval数据进行特征选择

    参数:
    df_trainval: 训练验证集数据
    all_feature_cols: 所有特征列名
    thresholds: 相关性阈值列表

    返回:
    grid_result: 包含不同threshold和对应选择特征的DataFrame
    """
    logging.info("=== Generating feature selection grid ===")

    grid_result = pd.DataFrame(columns=['threshold', 'num_features', 'features'])

    for threshold in thresholds:
        logging.info(f"Processing threshold: {threshold:.3f}")

        # 仅在trainval数据上进行特征选择
        selected_cols = remove_highly_correlated_features(
            df_trainval,
            all_feature_cols,
            threshold=threshold,
            plot=False  # 关闭绘图
        )

        tmp = pd.DataFrame({
            'threshold': [threshold],
            'num_features': [len(selected_cols)],
            'features': [selected_cols]
        })
        grid_result = pd.concat([grid_result, tmp], ignore_index=True)

        logging.info(f"Threshold {threshold:.3f}: {len(selected_cols)} features selected")

    return grid_result


def cox_objective_with_features(trial, df_trainval, target_features, params, round_idx):
    """
    Optuna目标函数：使用指定特征训练Cox模型并返回交叉验证C-index

    参数:
    trial: Optuna trial对象
    df_trainval: 训练验证集
    target_features: 选择的特征列表
    params: 其他参数
    round_idx: 轮次索引

    返回:
    平均交叉验证C-index
    """

    if len(target_features) == 0:
        return 0.5  # 没有特征时返回随机水平

    # 超参数建议
    backend = trial.suggest_categorical('backend', ['Cox'])
    reweighting = trial.suggest_categorical('reweighting', ['None'])

    # Cox模型特有的超参数

    penalizer = trial.suggest_float('penalizer', 1e-5, 1e-1, log=True)
    l1_ratio = trial.suggest_float('l1_ratio', 0.0, 1.0, step=0.5)  # 0表示Ridge, 1表示Lasso, 中间值表示Elastic Net


    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

    # 交叉验证
    cv_splits = params.get('n_splits', 5)
    cv = StratifiedKFold(
        n_splits=cv_splits,
        shuffle=True,
        random_state=params['seed'] + round_idx + trial.number
    )

    cv_scores = []

    for fold_idx, (train_idx, val_idx) in enumerate(cv.split(df_trainval, df_trainval[event_col])):
        try:
            # 分割数据
            df_train_fold = df_trainval.iloc[train_idx].copy()
            df_val_fold = df_trainval.iloc[val_idx].copy()

            # 标准化
            df_train_fold_scaled = df_train_fold.copy()

            df_val_fold_scaled = df_val_fold.copy()

            # 计算权重（如果需要）
            X_train_np = df_train_fold_scaled[target_features].values
            n_train, p_train = X_train_np.shape


            # 训练Cox模型
            if backend == 'Cox':
                # 使用lifelines Cox模型
                cox_data = df_train_fold_scaled[target_features + [duration_col, event_col]].copy()

                # 创建CoxPHFitter实例，添加penalizer和l1_ratio参数
                cph = CoxPHFitter(penalizer=penalizer, l1_ratio=l1_ratio)

                cph.fit(cox_data, duration_col=duration_col, event_col=event_col)

                # 验证集预测
                val_predictions = cph.predict_partial_hazard(df_val_fold_scaled[target_features])

            # 计算C-index
            c_index = concordance_index(
                df_val_fold[duration_col],
                -val_predictions,  # 负号因为concordance_index期望风险分数
                df_val_fold[event_col]
            )

            cv_scores.append(c_index)

        except Exception as e:
            logging.warning(f"Error in fold {fold_idx}: {str(e)}")
            cv_scores.append(0.5)  # 出错时返回随机水平

    mean_cv_score = np.mean(cv_scores)
    return mean_cv_score


def optuna_optimization_with_features(df_trainval, grid_result, params, round_idx):
    """
    对每个特征选择结果使用Optuna进行超参数优化

    参数:
    df_trainval: 训练验证集
    grid_result: 特征选择结果
    params: 参数字典
    round_idx: 轮次索引

    返回:
    optimization_results: 优化结果列表
    """
    logging.info("=== Starting Optuna optimization for different feature sets ===")

    optimization_results = []

    for idx, row in grid_result.iterrows():
        threshold = row['threshold']
        num_features = row['num_features']
        target_features = row['features']

        logging.info(f"Optimizing with threshold {threshold:.3f}, {num_features} features")

        if num_features == 0:
            logging.warning(f"No features selected for threshold {threshold:.3f}, skipping")
            continue

        # 创建Optuna study
        study_name = f"cox_features_t{threshold:.3f}_r{round_idx}"
        sampler = TPESampler(seed=params['seed'] + round_idx)
        study = optuna.create_study(
            direction='maximize',
            sampler=sampler,
            study_name=study_name
        )

        # 定义目标函数（使用partial来传递额外参数）
        def objective(trial):
            return cox_objective_with_features(trial, df_trainval, target_features, params, round_idx)

        # 进行优化
        n_trials = params.get('n_trials', 50)
        try:
            study.optimize(objective, n_trials=n_trials, timeout=params.get('timeout', 300))

            # 记录结果
            best_trial = study.best_trial
            optimization_results.append({
                'threshold': threshold,
                'num_features': num_features,
                'features': target_features,
                'best_cv_score': best_trial.value,
                'best_params': best_trial.params,
                'n_trials': len(study.trials)
            })

            logging.info(f"Threshold {threshold:.3f}: Best CV C-index = {best_trial.value:.4f}")

        except Exception as e:
            logging.error(f"Error optimizing threshold {threshold:.3f}: {str(e)}")
            optimization_results.append({
                'threshold': threshold,
                'num_features': num_features,
                'features': target_features,
                'best_cv_score': 0.5,
                'best_params': {},
                'n_trials': 0
            })

    return optimization_results


def train_final_model_and_evaluate(df_trainval, df_test, best_result, params, round_idx):
    """
    使用最佳特征集和超参数在整个trainval上训练最终模型，然后在test集上评估

    参数:
    df_trainval: 训练验证集
    df_test: 测试集
    best_result: 最佳优化结果
    params: 参数字典
    round_idx: 轮次索引

    返回:
    final_test_c_index: 最终测试集C-index
    """
    logging.info("=== Training final model and evaluation ===")

    target_features = best_result['features']
    best_params = best_result['best_params']

    if len(target_features) == 0:
        logging.warning("No features available for final model")
        return 0.5

    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")



    df_trainval_scaled = df_trainval.copy()

    df_test_scaled = df_test.copy()

    # 训练最终模型
    backend = best_params.get('backend', 'Cox')

    try:
        if backend == 'Cox':
            # 使用lifelines Cox模型，应用优化后的超参数
            cox_data = df_trainval_scaled[target_features + [duration_col, event_col]].copy()

            # 从best_params中获取优化后的超参数
            penalizer = best_params.get('penalizer', 0.01)
            l1_ratio = best_params.get('l1_ratio', 0.0)

            # 创建CoxPHFitter实例，使用优化后的参数
            cph = CoxPHFitter(penalizer=penalizer, l1_ratio=l1_ratio)
            cph.fit(cox_data, duration_col=duration_col, event_col=event_col)

            # 测试集预测
            test_predictions = cph.predict_partial_hazard(df_test_scaled[target_features])

        # 计算最终测试集C-index
        final_test_c_index = concordance_index(
            df_test_scaled[duration_col],
            -test_predictions,  # 负号因为concordance_index期望风险分数
            df_test_scaled[event_col]
        )

        return final_test_c_index

    except Exception as e:
        logging.error(f"Error in final model training: {str(e)}")
        return 0.5


def objective(n_trials, params, round_idx, logger, PATH, df_trainval, df_test, feature_cols):
    logging.info(f"--- Starting Round {round_idx} ---")
    # setup_seed(params['seed'] + round_idx)

    logging.info(f"Data split - Train/Val: {df_trainval.shape}, Test: {df_test.shape}")

    # ===============================
    # 阶段 1：特征选择网格生成（仅在trainval上）
    # ===============================
    logging.info("=== Stage 1: Feature selection grid generation ===")

    # 定义相关性阈值范围
    thresholds = np.arange(0.15, 0.5, 0.05)  # 从0.5到0.85，步长0.05

    # 在trainval数据上生成特征选择网格
    grid_result = generate_feature_selection_grid(df_trainval, feature_cols, thresholds)

    logging.info(f"Generated feature selection grid with {len(grid_result)} threshold settings")

    # ===============================
    # 阶段 2：Optuna超参数优化（每个特征集）
    # ===============================
    logging.info("=== Stage 2: Optuna hyperparameter optimization ===")

    optimization_results = optuna_optimization_with_features(
        df_trainval, grid_result, params, round_idx
    )

    if not optimization_results:
        logging.error("No successful optimization results obtained")
        return {}

    # ===============================
    # 阶段 3：选择最佳特征集和超参数
    # ===============================
    logging.info("=== Stage 3: Selecting best feature set and hyperparameters ===")

    # 根据CV分数选择最佳结果
    best_result = max(optimization_results, key=lambda x: x['best_cv_score'])

    logging.info(f"Best feature set: threshold={best_result['threshold']:.3f}, "
                f"num_features={best_result['num_features']}, "
                 f"Feature names={best_result['features']}, "
                f"cv_score={best_result['best_cv_score']:.4f}")

    # ===============================
    # 阶段 4：最终模型训练和评估
    # ===============================
    logging.info("=== Stage 4: Final model training and evaluation ===")

    final_test_c_index = train_final_model_and_evaluate(
        df_trainval, df_test, best_result, params, round_idx
    )

    if final_test_c_index is not None:
        logging.info(f"Final test C-index: {final_test_c_index:.4f}")

        return {
            "best_threshold": best_result['threshold'],
            "best_num_features": best_result['num_features'],
            "best_cv_score": best_result['best_cv_score'],
            "final_test_c_index": final_test_c_index,
            "best_params": best_result['best_params'],
            "all_optimization_results": optimization_results
        }
    else:
        logging.warning("Final model training failed")
        return {}


if __name__ == "__main__":
    import time
    from datetime import datetime

    # 创建logs文件夹
    logs_dir = "logs"
    os.makedirs(logs_dir, exist_ok=True)

    params = {
        'PATH': r'processed_equipment_data_new.csv',
        'seed': 9,  # 固定基础种子
        'n_splits': 10,  # 交叉验证折数
        'n_trials': 40,  # Optuna试验次数
        'timeout': 300,  # 每个特征集的优化超时时间（秒）
        'times': 2,  # 总运行轮次
        'result_dir': 'results',
        'test_size': 0.1,
        # 新增参数来控制随机性
        # 'use_fixed_split': True,  # 是否使用固定的数据分割
        # 'increase_test_size': False,  # 是否增加测试集大小
    }

    comm = MPI.COMM_WORLD
    rank = comm.Get_rank()
    size = comm.Get_size()

    # 简化的日志设置：只有主进程保存日志
    if rank == 0:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_filename = os.path.join(logs_dir, f"DecoCox_Optuna_{timestamp}.log")

        # 配置文件和控制台双重输出
        file_handler = logging.FileHandler(log_filename, mode='w', encoding='utf-8')
        console_handler = logging.StreamHandler()

        # 设置格式
        formatter = logging.Formatter('%(asctime)s [%(levelname)s] %(message)s',
                                    datefmt='%Y-%m-%d %H:%M:%S')
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)

        # 获取根logger并添加处理器
        logger = logging.getLogger()
        logger.handlers.clear()  # 清除现有处理器
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        logger.setLevel(logging.INFO)

        logging.info("=== Decorrelated Cox Analysis with Optuna Optimization ===")
        logging.info(f"Logging to: {log_filename}")
        logging.info(f"MPI processes: {size}")
        logging.info(f"Parameters: {params}")

    else:
        # 非主进程：只有控制台输出，无文件日志
        console_handler = logging.StreamHandler()
        formatter = logging.Formatter('%(asctime)s [%(levelname)s] %(message)s',
                                    datefmt='%Y-%m-%d %H:%M:%S')
        console_handler.setFormatter(formatter)

        logger = logging.getLogger()
        logger.handlers.clear()
        logger.addHandler(console_handler)
        logger.setLevel(logging.INFO)

    # 创建结果目录
    os.makedirs(params['result_dir'], exist_ok=True)

    # 记录开始时间
    start_time = time.time()
    PATH = params['PATH']
    df_trainval, df_test, feature_cols = data_processing(PATH)

    # 在MPI进程间分配轮次
    local_results = []
    rounds_per_process = params['times'] // size
    remainder = params['times'] % size

    # 分配轮次
    if rank < remainder:
        local_rounds = rounds_per_process + 1
        start_round = rank * (rounds_per_process + 1)
    else:
        local_rounds = rounds_per_process
        start_round = rank * rounds_per_process + remainder

    logging.info(f"Process {rank}: Running {local_rounds} rounds starting from round {start_round}")

    # 改为字典结构来收集结果，与RSF_v2.py保持一致
    local_results_dict = dd(list)

    for i in range(local_rounds):
        round_idx = start_round + i
        logging.info(f"Process {rank}: Starting round {round_idx + 1}/{params['times']}")

        try:
            result = objective(
                n_trials=params['n_trials'],
                params=params,
                round_idx=round_idx,
                logger=logging,
                PATH=params['PATH'],
                df_trainval=df_trainval,
                df_test=df_test,
                feature_cols=feature_cols
            )

            if result:
                # 将结果按指标分类存储到字典中
                for k, v in result.items():
                    local_results_dict[k].append(v)
                logging.info(f"Round {round_idx + 1} completed successfully")
            else:
                logging.warning(f"Round {round_idx + 1} failed")

        except Exception as e:
            logging.error(f"Error in round {round_idx + 1}: {str(e)}")

    # 收集所有进程的结果
    all_results = comm.gather(local_results_dict, root=0)

    # Process and save results (only main process)
    if rank == 0:
        # Flatten results
        final_results_list = dd(list)
        # for process_results in all_results:
        #     final_results.extend(process_results)
        for process_results in all_results:
            for k, v in process_results.items():
                final_results_list[k].extend(v)

        if final_results_list:
            logging.info("=" * 70)
            logging.info(f"FINAL RESULTS SUMMARY - {params['times']} Independent Runs")
            logging.info("=" * 70)

            for metric_name, value_list in final_results_list.items():
                if metric_name == 'best_params':
                    continue

                numeric_values = [v for v in value_list if isinstance(v, (int, float, np.number))]

                if numeric_values:
                    final_mean = np.mean(numeric_values)
                    final_std = np.std(numeric_values)
                    cv = (final_std / final_mean) * 100 if final_mean != 0 else 0
                    logging.info(f"{metric_name}: Mean={final_mean:.4f}, Std={final_std:.4f}, CV={cv:.2f}%")

            if 'final_test_c_index' in final_results_list:
                final_test_scores = final_results_list['final_test_c_index']
                final_test_scores = [s for s in final_test_scores if isinstance(s, (int, float, np.number))]

                if final_test_scores:
                    logging.info("-" * 50)
                    logging.info("TEST SET PERFORMANCE ANALYSIS")
                    logging.info("-" * 50)
                    logging.info(f"Test C-indices: {[f'{s:.4f}' for s in final_test_scores]}")

                    mean_score = np.mean(final_test_scores)
                    std_score = np.std(final_test_scores)
                    cv_score = (std_score / mean_score) * 100 if mean_score != 0 else 0
                    logging.info("Final Test Set Performance Summary:")
                    logging.info(f"Final Test C-indices: {[f'{s:.4f}' for s in final_test_scores]}")
                    logging.info(f"Mean Final Test C-index: {np.mean(final_test_scores):.4f}")
                    logging.info(f"Best Final Test C-index: {np.max(final_test_scores):.4f}")
                    logging.info(f"Std Dev of Final Test C-index: {np.std(final_test_scores):.4f}")
                    logging.info(f"Worst Final Test C-index: {np.min(final_test_scores):.4f}")

                    # 判断方差是否过大
                    if cv_score > 10:
                        logging.warning(f"High variance detected (CV={cv_score:.2f}%)! Consider:")
                        logging.warning("1. Increasing test set size")
                        logging.warning("2. Using more Optuna trials")
                        logging.warning("3. Fixing data splits across runs")
                        logging.warning("4. Ensemble methods")

            # 转换为DataFrame进行分析
            # results_df = pd.DataFrame(final_results)
            #
            # # 计算统计信息
            # cv_scores = results_df['best_cv_score'].values
            # test_scores = results_df['final_test_c_index'].values
            # thresholds = results_df['best_threshold'].values
            # num_features = results_df['best_num_features'].values
            #
            # logging.info("=== Final Results Summary ===")
            # logging.info(f"Total successful rounds: {len(final_results)}")
            # logging.info(f"CV C-index - Mean: {np.mean(cv_scores):.4f} ± {np.std(cv_scores):.4f}")
            # logging.info(f"Test C-index - Mean: {np.mean(test_scores):.4f} ± {np.std(test_scores):.4f}")
            # logging.info(f"Best threshold - Mean: {np.mean(thresholds):.3f} ± {np.std(thresholds):.3f}")
            # logging.info(f"Selected features - Mean: {np.mean(num_features):.1f} ± {np.std(num_features):.1f}")
            # logging.info(f"CV C-index - Min: {np.min(cv_scores):.4f}, Max: {np.max(cv_scores):.4f}")
            # logging.info(f"Test C-index - Min: {np.min(test_scores):.4f}, Max: {np.max(test_scores):.4f}")

            # 保存详细结果
            # timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            # results_file = os.path.join(params['result_dir'], f"DecoCox_results_{timestamp}.csv")
            # results_df.to_csv(results_file, index=False)
            # logging.info(f"Detailed results saved to: {results_file}")

        else:
            logging.error("No successful results obtained!")

        total_time = time.time() - start_time
        logging.info(f"Total execution time: {total_time:.2f} seconds")
        logging.info("=== Execution completed ===")
