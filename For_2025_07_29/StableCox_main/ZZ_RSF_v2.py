# 导入必要的库
from data.selection_bias import gen_selection_bias_data
from algorithm.DWR import DWR
from algorithm.SRDO import SRDO
from model.linear import get_algorithm_class
from metrics import get_metric_class
from utils import setup_seed, get_beta_s, get_expname, calc_var, pretty, get_cov_mask, BV_analysis
from Logger import Logger
from model.STG import STG
from sksurv.ensemble import RandomSurvivalForest
from sksurv.metrics import concordance_index_censored
from sklearn.metrics import mean_squared_error, f1_score, r2_score
import numpy as np
import os
import torch
from collections import defaultdict as dd
import pandas as pd
from sklearn.preprocessing import StandardScaler
from lifelines import CoxPHFitter
import seaborn as sns
import matplotlib.pyplot as plt
from lifelines.statistics import logrank_test
from lifelines import KaplanMeierFitter
from sksurv.util import Surv
from lifelines.utils import concordance_index
from sklearn.metrics import roc_auc_score, accuracy_score
import logging
import warnings
warnings.filterwarnings('ignore')
from mpi4py import MPI
import time
from sklearn.model_selection import RandomizedSearchCV, StratifiedKFold
from scipy.stats import randint, uniform

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S"
)

duration_col = 'duration'
event_col = 'event'
SN_col = 'SN_Common'


def data_processing(PATH):
    # 1. 加载全数据集 df
    try:
        df = pd.read_csv(PATH)
        logging.info(f"Successfully loaded data from {PATH}. Shape: {df.shape}")
    except FileNotFoundError:
        logging.error(f"Data file not found at {PATH}. Please check path.")
        return {}

    # 数据预处理
    if SN_col in df.columns:
        df = df.drop(columns=[SN_col])
    problematic_cols = ['0A_Flattop', 'Ambient_HST_delta', 'Ambient_temp', 'Offset_cal']
    cols_to_drop = [col for col in problematic_cols if col in df.columns]
    df = df.drop(columns=cols_to_drop)
    feature_cols = [col for col in df.columns if col not in [duration_col, event_col]]

    from sklearn.model_selection import train_test_split

    # 2. 第一次分割：全数据 -> 训练验证集 + 测试集

    df_trainval, df_test = train_test_split(
        df,
        test_size=params['test_size'],
        # random_state=params['seed'],
        stratify=df[event_col]
    )
    logging.info(f'Data scaler splite with random state {params["seed"]}')
    scaler = StandardScaler()
    scaler.fit(df_trainval[feature_cols])
    df_trainval[feature_cols] = scaler.transform(df_trainval[feature_cols])
    df_test[feature_cols] = scaler.transform(df_test[feature_cols])

    return df_trainval, df_test, feature_cols

class RSFConcordanceScorer:
    """
    自定义的RSF C-index评分器，用于RandomizedSearchCV
    """
    def __init__(self, greater_is_better=True):
        self.greater_is_better = greater_is_better

    def __call__(self, estimator, X, y):
        """
        计算C-index评分
        """
        try:
            predictions = estimator.predict(X)
            c_index = concordance_index_censored(y['event'], y['duration'], predictions)[0]
            return c_index
        except Exception as e:
            logging.warning(f"Error in scoring: {str(e)}")
            return 0.5  # 返回随机水平


def cross_validation_randomized_search(df_trainval, feature_cols, params, round_idx):
    """
    阶段1：使用RandomizedSearchCV进行超参数优化
    在trainval上进行交叉验证，返回最佳参数和最佳验证集C-index
    """
    logging.info("=== Start RandomizedSearchCV hyperparameter optimization ===")

    from sklearn.model_selection import StratifiedKFold

    # 准备数据
    X_trainval = df_trainval[feature_cols].values
    y_trainval_struct = Surv.from_dataframe(event_col, duration_col, df_trainval)

    # 定义RSF参数搜索空间
    param_distributions = {
        'n_estimators': randint(10, 300),
        'min_samples_split': randint(2, 20),
        'min_samples_leaf': randint(1, 15),
        'max_features': [None, 'sqrt', 'log2'] + [0.3, 0.5, 0.7, 0.9],
        'max_depth': [None] + list(range(5, 25)),
        'bootstrap': [True, False],
    }

    # 创建基础RSF模型
    base_rsf = RandomSurvivalForest(
        # n_jobs=-1,
        random_state=params['seed'] + round_idx
    )

    # 设置交叉验证
    cv_splits = params.get('n_splits', 10)  # 限制CV折数以减少计算
    cv = StratifiedKFold(
        n_splits=cv_splits,
        shuffle=True,
        random_state=params['seed'] + round_idx
    )

    # 自定义评分器
    scorer = RSFConcordanceScorer()

    # 创建RandomizedSearchCV
    n_iter = params.get('n_iter', 30)  # RandomizedSearchCV的迭代次数

    random_search = RandomizedSearchCV(
        estimator=base_rsf,
        param_distributions=param_distributions,
        n_iter=n_iter,
        cv=cv,
        scoring=scorer,
        n_jobs=-1,  # 由于RSF内部已经并行，这里设为1避免过度并行
        random_state=params['seed'] + round_idx,
        verbose=1
    )

    logging.info(f"Start RandomizedSearchCV, {n_iter} iterations, {cv_splits} fold cross validation")

    # 执行搜索
    random_search.fit(X_trainval, y_trainval_struct)

    best_params = random_search.best_params_
    best_cv_score = random_search.best_score_

    logging.info(f"RandomizedSearchCV completed! Best validation set C-index:{best_cv_score:.4f}")
    logging.info(f"Best parameters: {best_params}")

    return random_search.best_estimator_, best_params, best_cv_score


def train_final_model_with_best_params(df_trainval, df_test, feature_cols, best_params, round_idx):
    """
    阶段2：用最佳超参数在整个trainval上训练最终模型，然后在test集上评估
    """
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

    # 在整个trainval上训练标准化器
    scaler_final = StandardScaler()
    scaler_final.fit(df_trainval[feature_cols])
    df_trainval_scaled = df_trainval.copy()
    df_trainval_scaled[feature_cols] = scaler_final.transform(df_trainval[feature_cols])

    # 构造sksurv格式的标签
    y_trainval_struct = Surv.from_dataframe(event_col, duration_col, df_trainval_scaled)

    # 用最佳参数训练最终RSF模型
    # 从best_params中提取RSF参数
    rsf_params = {k: v for k, v in best_params.items()
                  if k in ['n_estimators', 'min_samples_split', 'min_samples_leaf',
                          'max_features', 'max_depth', 'bootstrap']}

    final_model = RandomSurvivalForest(
        **rsf_params,
        n_jobs=-1,
        random_state=best_params.get('seed', 42) + round_idx
    )

    # 训练最终模型
    if best_params.get('reweighting', 'None') != 'None':
        # RSF目前不直接支持样本权重，这里我们可以通过bootstrap采样来模拟权重效果
        # 或者简单地使用原始数据训练（更常见的做法）
        logging.warning("RSF does not directly support sample weights. Training without reweighting.")

    final_model.fit(df_trainval_scaled[feature_cols].values, y_trainval_struct)
    logging.info("Final RSF model trained on entire trainval set")

    # 准备测试集
    df_test_scaled = df_test.copy()
    df_test_scaled[feature_cols] = scaler_final.transform(df_test[feature_cols])
    y_test_struct = Surv.from_dataframe(event_col, duration_col, df_test_scaled)

    # 在测试集上评估
    test_predictions = final_model.predict(df_test_scaled[feature_cols].values)
    final_test_c_index = concordance_index_censored(
        y_test_struct['event'],
        y_test_struct['duration'],
        test_predictions
    )[0]

    return final_test_c_index, feature_cols, final_model


def objective(n_iter, params, round_idx, logger, PATH, df_trainval, df_test, feature_cols):
    logging.info(f"--- Starting Round {round_idx} ---")


    logging.info(f"Data split - Train/Val: {df_trainval.shape}, Test: {df_test.shape}")

    # ===============================
    # 阶段 1：超参数优化（用 RandomizedSearchCV）
    # ===============================
    logging.info("=== Stage 1: Starting hyperparameter optimization ===")

    # 适应性调整搜索迭代次数
    if params.get('use_fixed_split', False):
        n_iter_reduced = min(20, n_iter if n_iter else 20)
    else:
        n_iter_reduced = min(30, n_iter if n_iter else 30)

    # 更新参数
    search_params = params.copy()
    search_params['n_iter'] = n_iter_reduced

    logging.info(f"Use {n_iter_reduced} iterations of RandomizedSearchCV")

    best_model, best_params_found, best_cv_score = cross_validation_randomized_search(
        df_trainval, feature_cols, search_params, round_idx
    )

    # ===============================
    # 阶段 2：最终模型评估
    # ===============================
    logging.info("=== Stage 2: Final model training and evaluation ===")

    # 合并找到的最佳参数和原始参数
    final_params = params.copy()
    final_params.update(best_params_found)

    final_test_c_index, selected_features, final_model = train_final_model_with_best_params(
        df_trainval, df_test, feature_cols, final_params, round_idx
    )

    if final_test_c_index is not None:
        logging.info(f"Final test C-index: {final_test_c_index:.4f}")
        logging.info(f"Number of selected features: {len(selected_features)}")

        return {
            "best_cv_score": best_cv_score,
            "final_test_c_index": final_test_c_index,
            "best_params": best_params_found,
            "selected_features_count": len(selected_features)
        }
    else:
        logging.warning("Final model training failed")
        return {}


if __name__ == "__main__":
    import time
    from datetime import datetime

    # 创建logs文件夹
    logs_dir = "logs"
    os.makedirs(logs_dir, exist_ok=True)

    params = {
        'PATH': r'processed_equipment_data_new.csv',
        'seed': 9,  # 固定基础种子
        'n_splits': 10,  # 交叉验证折数
        'reweighting': 'None',  # RSF通常不需要重新加权 ['DWR', 'SRDO', 'None']
        'decorrelation_type': 'global',
        'iters_balance': 500,
        'times': 10,
        'result_dir': 'results',
        # 新增参数来控制随机性
        'use_fixed_split': True,  # 是否使用固定的数据分割
        'increase_test_size': False,  # 是否增加测试集大小
        # RSF特有参数（搜索空间的默认值）
        'n_estimators': 100,
        'min_samples_split': 10,
        'min_samples_leaf': 5,
        'max_features': None,
        'max_depth': None,
        'bootstrap': True,
    }

    comm = MPI.COMM_WORLD
    rank = comm.Get_rank()
    size = comm.Get_size()

    # 简化的日志设置：只有主进程保存日志
    if rank == 0:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_filename = os.path.join(logs_dir, f"RSF_RandomizedSearch_{timestamp}.log")

        # 配置文件和控制台双重输出
        file_handler = logging.FileHandler(log_filename, mode='w', encoding='utf-8')
        console_handler = logging.StreamHandler()

        # 设置格式
        formatter = logging.Formatter('%(asctime)s [%(levelname)s] %(message)s',
                                    datefmt='%Y-%m-%d %H:%M:%S')
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)

        # 获取根logger并添加处理器
        logger = logging.getLogger()
        logger.handlers.clear()  # 清除现有处理器
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        logger.setLevel(logging.INFO)

        logging.info(f"RSF RandomizedSearchCV Optimization Started")
        logging.info(f"Log file: {log_filename}")
        logging.info(f"MPI Configuration: {size} processes")
        logging.info(f"Parameters: {params}")

        # # RSF参数说明
        # logging.info("RSF Parameter Optimization ENABLED:")
        # logging.info("  - n_estimators: [50, 300]")
        # logging.info("  - min_samples_split: [2, 20]")
        # logging.info("  - min_samples_leaf: [1, 15]")
        # logging.info("  - max_features: [None, 'sqrt', 'log2', 0.3, 0.5, 0.7, 0.9]")
        # logging.info("  - max_depth: [None, 5-25]")
        # logging.info("  - bootstrap: [True, False]")

    PATH = params['PATH']
    begin_time = time.time()
    df_trainval, df_test, feature_cols = data_processing(PATH)
    setup_seed(params['seed'])

    results_list = dd(list)
    results_list_test = dd(list)

    all_indices = list(range(params['times']))
    my_indices = [i for i in all_indices if i % size == rank]

    my_results_list = dd(list)
    my_results_list_test = dd(list)

    for i in my_indices:
        if rank == 0:  # 只有主进程记录详细日志
            logging.info(f"Starting Round {i+1}/{params['times']}")
        results_per_run = objective(25, params, i, logging, PATH, df_trainval, df_test, feature_cols)  # 25次RandomizedSearchCV迭代
        if results_per_run:
            for k, v in results_per_run.items():
                my_results_list[k].append(v)

    # 收集所有进程的结果到主进程
    all_results_list = comm.gather(my_results_list, root=0)

    if rank == 0:
        final_results_list = dd(list)
        for res in all_results_list:
            for k, v in res.items():
                final_results_list[k].extend(v)

        # 统计和输出结果
        if final_results_list:
            logging.info("=" * 70)
            logging.info(f"FINAL RESULTS SUMMARY - {params['times']} Independent Runs (RSF)")
            logging.info("=" * 70)

            for metric_name, value_list in final_results_list.items():
                # 跳过非数值类型的数据（如best_params字典）
                if metric_name == 'best_params':
                    continue

                # 确保value_list中都是数值
                numeric_values = [v for v in value_list if isinstance(v, (int, float, np.number))]

                if numeric_values:
                    final_mean = np.mean(numeric_values)
                    final_std = np.std(numeric_values)
                    # 计算变异系数
                    cv = (final_std / final_mean) * 100 if final_mean != 0 else 0
                    logging.info(f"{metric_name}: Mean={final_mean:.4f}, Std={final_std:.4f}, CV={cv:.2f}%")
                else:
                    logging.info(f"'{metric_name}': No numeric values to calculate statistics")

            if 'final_test_c_index' in final_results_list:
                final_test_scores = final_results_list['final_test_c_index']
                # 确保都是数值
                final_test_scores = [s for s in final_test_scores if isinstance(s, (int, float, np.number))]

                if final_test_scores:
                    logging.info("-" * 50)
                    logging.info("TEST SET PERFORMANCE ANALYSIS")
                    logging.info("-" * 50)
                    logging.info(f"Test C-indices: {[f'{s:.4f}' for s in final_test_scores]}")

                    mean_score = np.mean(final_test_scores)
                    std_score = np.std(final_test_scores)
                    cv_score = (std_score / mean_score) * 100 if mean_score != 0 else 0

                    logging.info("Final Test Set Performance Summary:")
                    logging.info(f"Final Test C-indices: {[f'{s:.4f}' for s in final_test_scores]}")
                    logging.info(f"Mean Final Test C-index: {np.mean(final_test_scores):.4f}")
                    logging.info(f"Best Final Test C-index: {np.max(final_test_scores):.4f}")
                    logging.info(f"Std Dev of Final Test C-index: {np.std(final_test_scores):.4f}")
                    logging.info(f"Worst Final Test C-index: {np.min(final_test_scores):.4f}")
                    # 判断方差是否过大
                    if cv_score > 10:
                        logging.warning("!" * 50)
                        logging.warning(f"HIGH VARIANCE DETECTED (CV={cv_score:.2f}%)")
                        logging.warning("Consider: 1) Increase test size 2) More trials 3) Fixed splits")
                        logging.warning("!" * 50)

                    logging.info("=" * 70)

                    # 保存最佳参数示例
                    if 'best_params' in final_results_list and final_results_list['best_params']:
                        best_example = final_results_list['best_params'][0]
                        logging.info("EXAMPLE OF OPTIMIZED PARAMETERS:")
                        for key, value in best_example.items():
                            if key not in ['PATH', 'seed', 'times', 'result_dir']:
                                logging.info(f"  {key}: {value}")
                        logging.info("=" * 70)
                else:
                    logging.info("No valid test scores to analyze")
        else:
            logging.info("No results were generated from the runs.")

        total_time = time.time() - begin_time
        logging.info(f"TOTAL EXECUTION TIME: {total_time:.2f} seconds")
        logging.info(f"Results saved to: {log_filename}")

    # 所有进程等待主进程完成
    comm.Barrier()
    if rank != 0:
        print(f"Rank {rank}: Completed")

# 推荐的运行命令:
# mpiexec -n 4 python ZZ_RSF_v1.py
