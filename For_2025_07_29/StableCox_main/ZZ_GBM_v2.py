# 导入必要的库
from data.selection_bias import gen_selection_bias_data
from algorithm.DWR import DWR
from algorithm.SRDO import SRDO
from model.linear import get_algorithm_class
from metrics import get_metric_class
from utils import setup_seed, get_beta_s, get_expname, calc_var, pretty, get_cov_mask, BV_analysis
from Logger import Logger
from model.STG import STG
from sksurv.ensemble import GradientBoostingSurvivalAnalysis
from sksurv.metrics import concordance_index_censored
from sklearn.metrics import mean_squared_error, f1_score, r2_score
import numpy as np
import os
import torch
from collections import defaultdict as dd
import pandas as pd
from sklearn.preprocessing import StandardScaler
from lifelines import CoxPHFitter
import seaborn as sns
import matplotlib.pyplot as plt
from lifelines.statistics import logrank_test
from lifelines import KaplanMeierFitter
from sksurv.util import Surv
from lifelines.utils import concordance_index
from sklearn.metrics import roc_auc_score, accuracy_score
import logging
import warnings
warnings.filterwarnings('ignore')
from mpi4py import MPI
import time
from sklearn.model_selection import RandomizedSearchCV, StratifiedKFold
from scipy.stats import randint, uniform, loguniform

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S"
)

duration_col = 'duration'
event_col = 'event'
SN_col = 'SN_Common'


def data_processing(PATH):
    # 1. 加载全数据集 df
    try:
        df = pd.read_csv(PATH)
        logging.info(f"Successfully loaded data from {PATH}. Shape: {df.shape}")
    except FileNotFoundError:
        logging.error(f"Data file not found at {PATH}. Please check path.")
        return {}

    # 数据预处理
    if SN_col in df.columns:
        df = df.drop(columns=[SN_col])
    problematic_cols = ['0A_Flattop', 'Ambient_HST_delta', 'Ambient_temp', 'Offset_cal']
    cols_to_drop = [col for col in problematic_cols if col in df.columns]
    df = df.drop(columns=cols_to_drop)
    feature_cols = [col for col in df.columns if col not in [duration_col, event_col]]

    from sklearn.model_selection import train_test_split

    # 2. 第一次分割：全数据 -> 训练验证集 + 测试集

    df_trainval, df_test = train_test_split(
        df,
        test_size=params['test_size'],
        # random_state=params['seed'],
        stratify=df[event_col]
    )
    logging.info(f'Data scaler splite with random state {params["seed"]}')
    scaler = StandardScaler()
    scaler.fit(df_trainval[feature_cols])
    df_trainval[feature_cols] = scaler.transform(df_trainval[feature_cols])
    df_test[feature_cols] = scaler.transform(df_test[feature_cols])

    return df_trainval, df_test, feature_cols

class GBMConcordanceScorer:
    """
    Custom GBM C-index scorer for RandomizedSearchCV
    """
    def __init__(self, greater_is_better=True):
        self.greater_is_better = greater_is_better

    def __call__(self, estimator, X, y):
        """
        Calculate C-index score
        """
        try:
            predictions = estimator.predict(X)
            c_index = concordance_index_censored(y['event'], y['duration'], predictions)[0]
            return c_index
        except Exception as e:
            logging.warning(f"Error in scoring: {str(e)}")
            return 0.5  # Return random level


def cross_validation_randomized_search(df_trainval, feature_cols, params):
    """
    Stage 1: Hyperparameter optimization using RandomizedSearchCV
    Perform cross-validation on trainval set, return best parameters and best validation C-index
    """
    logging.info("=== Starting RandomizedSearchCV hyperparameter optimization ===")

    from sklearn.model_selection import StratifiedKFold

    # Prepare data
    X_trainval = df_trainval[feature_cols].values
    y_trainval_struct = Surv.from_dataframe(event_col, duration_col, df_trainval)

    # Define GBM parameter search space
    param_distributions = {
        'n_estimators': randint(50, 500),
        'learning_rate': loguniform(0.01, 0.3),
        'max_depth': randint(3, 10),
        'min_samples_split': randint(2, 20),
        'min_samples_leaf': randint(1, 15),
        'max_features': [None, 'sqrt', 'log2'] + [0.3, 0.5, 0.7, 0.9],
        'subsample': uniform(0.6, 1.0),  # 0.6 to 1.0
        'max_leaf_nodes': [None] + list(range(10, 100, 10)),
        'dropout_rate': uniform(0.0, 0.3),  # 0.0 to 0.3
    }

    # Create base GBM model
    base_gbm = GradientBoostingSurvivalAnalysis()

    # Set cross-validation
    cv_splits = params.get('n_splits', 10)  # Limit CV folds to reduce computation
    cv = StratifiedKFold(
        n_splits=cv_splits,
        shuffle=True,
    )

    # Custom scorer
    scorer = GBMConcordanceScorer()

    # Create RandomizedSearchCV
    n_iter = params.get('n_iter', 30)  # Number of RandomizedSearchCV iterations

    random_search = RandomizedSearchCV(
        estimator=base_gbm,
        param_distributions=param_distributions,
        n_iter=n_iter,
        cv=cv,
        scoring=scorer,
        n_jobs=-1,  # GBM training is already quite intensive
        verbose=1
    )

    logging.info(f"Start RandomizedSearchCV, {n_iter} iterations, {cv_splits} fold cross validation")

    # Execute search
    random_search.fit(X_trainval, y_trainval_struct)

    best_params = random_search.best_params_
    best_cv_score = random_search.best_score_

    logging.info(f"RandomizedSearchCV completed! Best validation C-index: {best_cv_score:.4f}")
    logging.info(f"Best parameters: {best_params}")

    return random_search.best_estimator_, best_params, best_cv_score


def train_final_model_with_best_params(df_trainval, df_test, feature_cols, best_params):
    """
    Stage 2: Train final model with best hyperparameters on entire trainval set, then evaluate on test set
    """
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

    # Train scaler on entire trainval set
    df_trainval_scaled = df_trainval.copy()

    # Calculate weights (if SRDO/DWR is enabled)
    X_trainval_np = df_trainval_scaled[feature_cols].values
    n_trainval, p_trainval = X_trainval_np.shape

    # Construct sksurv format labels
    y_trainval_struct = Surv.from_dataframe(event_col, duration_col, df_trainval_scaled)

    # Train final GBM model with best parameters
    # Extract GBM parameters from best_params
    gbm_params = {k: v for k, v in best_params.items()
                  if k in ['n_estimators', 'learning_rate', 'max_depth', 'min_samples_split',
                          'min_samples_leaf', 'max_features', 'subsample', 'max_leaf_nodes',
                          'dropout_rate']}

    final_model = GradientBoostingSurvivalAnalysis(
        **gbm_params,
    )

    # Train final model

    final_model.fit(df_trainval_scaled[feature_cols].values, y_trainval_struct)
    logging.info("Final GBM model trained on entire trainval set")

    # Prepare test set
    df_test_scaled = df_test.copy()
    y_test_struct = Surv.from_dataframe(event_col, duration_col, df_test_scaled)

    # Evaluate on test set
    test_predictions = final_model.predict(df_test_scaled[feature_cols].values)
    final_test_c_index = concordance_index_censored(
        y_test_struct['event'],
        y_test_struct['duration'],
        test_predictions
    )[0]

    return final_test_c_index, feature_cols, final_model


def objective(n_iter, params, round_idx, logger, PATH, df_trainval, df_test, feature_cols):
    logging.info(f"--- Starting Round {round_idx} ---")
    # setup_seed(params['seed'] + round_idx)

    # 1. Load full dataset df


    logging.info(f"Data split - Train/Val: {df_trainval.shape}, Test: {df_test.shape}")

    # ===============================
    # Stage 1: Hyperparameter optimization (using RandomizedSearchCV)
    # ===============================
    logging.info("=== Stage 1: Starting hyperparameter optimization ===")

    # Adaptive adjustment of search iterations
    n_iter_reduced = n_iter


    # Update parameters
    search_params = params.copy()
    search_params['n_iter'] = n_iter_reduced

    logging.info(f"Use {n_iter_reduced} iterations of RandomizedSearchCV")

    best_model, best_params_found, best_cv_score = cross_validation_randomized_search(
        df_trainval, feature_cols, search_params
    )

    # ===============================
    # Stage 2: Final model evaluation
    # ===============================
    logging.info("=== Stage 2: Final model training and evaluation ===")

    # Merge found best parameters with original parameters
    final_params = params.copy()
    final_params.update(best_params_found)

    final_test_c_index, selected_features, final_model = train_final_model_with_best_params(
        df_trainval, df_test, feature_cols, final_params
    )

    if final_test_c_index is not None:
        logging.info(f"Final test C-index: {final_test_c_index:.4f}")
        logging.info(f"Number of selected features: {len(selected_features)}")

        return {
            "best_cv_score": best_cv_score,
            "final_test_c_index": final_test_c_index,
            "best_params": best_params_found,
            "selected_features_count": len(selected_features)
        }
    else:
        logging.warning("Final model training failed")
        return {}


if __name__ == "__main__":
    import time
    from datetime import datetime

    # Create logs folder
    logs_dir = "logs"
    os.makedirs(logs_dir, exist_ok=True)

    params = {
        'PATH': r'processed_equipment_data_new.csv',
        'seed': 9,  # Fixed base seed
        'n_splits': 10,  # Cross-validation folds
        'n_iter': 30,  # Number of RandomizedSearchCV iterations
        'times': 10,
        'test_size': 0.1,
        'result_dir': 'results',
        # New parameters to control randomness
        'use_fixed_split': True,  # Whether to use fixed data split
        'increase_test_size': False,  # Whether to increase test set size
        # GBM specific parameters (default values for search space)
        'n_estimators': 100,
        'learning_rate': 0.1,
        'max_depth': 6,
        'min_samples_split': 10,
        'min_samples_leaf': 5,
        'max_features': None,
        'subsample': 1.0,
        'max_leaf_nodes': None,
        'dropout_rate': 0.0,
        # SRDO specific parameters for hyperparameter optimization
        # 'hidden_layer_sizes': (64, 108),
        # 'W_clip_min': 0.004482532833926257,
        # 'W_clip_max': 2.6,
    }

    comm = MPI.COMM_WORLD
    rank = comm.Get_rank()
    size = comm.Get_size()

    # Simplified logging setup: only main process saves logs
    if rank == 0:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_filename = os.path.join(logs_dir, f"GBM_RandomizedSearch_{timestamp}.log")

        # Configure dual output to file and console
        file_handler = logging.FileHandler(log_filename, mode='w', encoding='utf-8')
        console_handler = logging.StreamHandler()

        # Set format
        formatter = logging.Formatter('%(asctime)s [%(levelname)s] %(message)s',
                                    datefmt='%Y-%m-%d %H:%M:%S')
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)

        # Get root logger and add handlers
        logger = logging.getLogger()
        logger.handlers.clear()  # Clear existing handlers
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        logger.setLevel(logging.INFO)

        logging.info("=== GBM Survival Analysis with Randomized Search ===")
        logging.info(f"Logging to: {log_filename}")
        logging.info(f"MPI processes: {size}")
        logging.info(f"Parameters: {params}")

    # else:
    #     # Non-main processes: only console output, no file logging
    #     console_handler = logging.StreamHandler()
    #     formatter = logging.Formatter('%(asctime)s [%(levelname)s] %(message)s',
    #                                 datefmt='%Y-%m-%d %H:%M:%S')
    #     console_handler.setFormatter(formatter)
    #
    #     logger = logging.getLogger()
    #     logger.handlers.clear()
    #     logger.addHandler(console_handler)
    #     logger.setLevel(logging.INFO)

    # Create results directory
    os.makedirs(params['result_dir'], exist_ok=True)

    # Record start time
    start_time = time.time()
    PATH = params['PATH']
    try:
        df_trainval, df_test, feature_cols = data_processing(PATH)
        logging.info(f"Successfully loaded data from {PATH}. Shape: ({len(df_trainval) + len(df_test), df_trainval.shape[1]})")
    except FileNotFoundError:
        logging.error(f"Data file not found at {PATH}. Please check path.")

    # Run multiple rounds across MPI processes
    local_results = []
    rounds_per_process = params['times'] // size
    remainder = params['times'] % size

    # Distribute rounds among processes
    if rank < remainder:
        local_rounds = rounds_per_process + 1
        start_round = rank * (rounds_per_process + 1)
    else:
        local_rounds = rounds_per_process
        start_round = rank * rounds_per_process + remainder

    logging.info(f"Process {rank}: Running {local_rounds} rounds starting from round {start_round}")

    for i in range(local_rounds):
        round_idx = start_round + i
        logging.info(f"Process {rank}: Starting round {round_idx + 1}/{params['times']}")

        try:
            result = objective(
                n_iter=params.get('n_iter', 30),
                params=params,
                round_idx=round_idx,
                logger=logging,
                PATH=params['PATH'],
                df_trainval=df_trainval,
                df_test=df_test,
                feature_cols=feature_cols
            )

            if result:
                result['round'] = round_idx
                result['process_rank'] = rank
                local_results.append(result)
                logging.info(f"Round {round_idx + 1} completed successfully")
            else:
                logging.warning(f"Round {round_idx + 1} failed")

        except Exception as e:
            logging.error(f"Error in round {round_idx + 1}: {str(e)}")

    # Gather results from all processes
    all_results = comm.gather(local_results, root=0)

    # Process and save results (only main process)
    if rank == 0:
        # Flatten results
        final_results_list = dd(list)
        # for process_results in all_results:
        #     final_results.extend(process_results)
        for process_results in all_results:
            for k,v in process_results.items():
                final_results_list[k].extend(v)

        if final_results_list:
            logging.info("=" * 70)
            logging.info(f"FINAL RESULTS SUMMARY - {params['times']} Independent Runs")
            logging.info("=" * 70)

            for metric_name, value_list in final_results_list.items():
                if metric_name == 'best_params':
                    continue

                numeric_values = [v for v in value_list if isinstance(v, (int, float, np.number))]

                if numeric_values:
                    final_mean = np.mean(numeric_values)
                    final_std = np.std(numeric_values)
                    cv = (final_std / final_mean) * 100 if final_mean != 0 else 0
                    logging.info(f"{metric_name}: Mean={final_mean:.4f}, Std={final_std:.4f}, CV={cv:.2f}%")

            if 'final_test_c_index' in final_results_list:
                final_test_scores = final_results_list['final_test_c_index']
                final_test_scores = [s for s in final_test_scores if isinstance(s, (int, float, np.number))]

                if final_test_scores:
                    logging.info("-" * 50)
                    logging.info("TEST SET PERFORMANCE ANALYSIS")
                    logging.info("-" * 50)
                    logging.info(f"Test C-indices: {[f'{s:.4f}' for s in final_test_scores]}")

                    mean_score = np.mean(final_test_scores)
                    std_score = np.std(final_test_scores)
                    cv_score = (std_score / mean_score) * 100 if mean_score != 0 else 0
                    logging.info("Final Test Set Performance Summary:")
                    logging.info(f"Final Test C-indices: {[f'{s:.4f}' for s in final_test_scores]}")
                    logging.info(f"Mean Final Test C-index: {np.mean(final_test_scores):.4f}")
                    logging.info(f"Best Final Test C-index: {np.max(final_test_scores):.4f}")
                    logging.info(f"Std Dev of Final Test C-index: {np.std(final_test_scores):.4f}")
                    logging.info(f"Worst Final Test C-index: {np.min(final_test_scores):.4f}")

                    # 判断方差是否过大
                    if cv_score > 10:
                        logging.warning(f"High variance detected (CV={cv_score:.2f}%)! Consider:")
                        logging.warning("1. Increasing test set size")
                        logging.warning("2. Using more Optuna trials")
                        logging.warning("3. Fixing data splits across runs")
                        logging.warning("4. Ensemble methods")
                    logging.info("=" * 70)

                # 保存最佳参数示例
                if 'best_params' in final_results_list and final_results_list['best_params']:
                    best_example = final_results_list['best_params'][0]
                    logging.info("EXAMPLE OF OPTIMIZED PARAMETERS:")
                    for key, value in best_example.items():
                        if key not in ['PATH', 'seed', 'times', 'result_dir']:
                            logging.info(f"  {key}: {value}")
                    logging.info("=" * 70)
            # Convert to DataFrame for analysis
            # results_df = pd.DataFrame(final_results)
            #
            # # Calculate statistics
            # cv_scores = results_df['best_cv_score'].values
            # test_scores = results_df['final_test_c_index'].values
            #
            # logging.info("=== Final Results Summary ===")
            # logging.info(f"Total successful rounds: {len(final_results)}")
            # logging.info(f"CV C-index - Mean: {np.mean(cv_scores):.4f} ± {np.std(cv_scores):.4f}")
            # logging.info(f"Test C-index - Mean: {np.mean(test_scores):.4f} ± {np.std(test_scores):.4f}")
            # logging.info(f"CV C-index - Min: {np.min(cv_scores):.4f}, Max: {np.max(cv_scores):.4f}")
            # logging.info(f"Test C-index - Min: {np.min(test_scores):.4f}, Max: {np.max(test_scores):.4f}")
            #
            # # Save detailed results
            # timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            # results_file = os.path.join(params['result_dir'], f"GBM_results_{timestamp}.csv")
            # results_df.to_csv(results_file, index=False)
            # logging.info(f"Detailed results saved to: {results_file}")

        else:
            logging.error("No successful results obtained!")

        total_time = time.time() - start_time
        logging.info(f"Total execution time: {total_time:.2f} seconds")
        logging.info("=== Execution completed ===")
