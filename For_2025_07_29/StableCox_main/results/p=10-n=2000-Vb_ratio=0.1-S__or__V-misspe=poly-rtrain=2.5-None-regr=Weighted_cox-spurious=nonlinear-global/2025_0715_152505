2025-07-15 15:25:05,682 - INFO: p: 10
2025-07-15 15:25:05,682 - INFO: n: 2000
2025-07-15 15:25:05,683 - INFO: V_ratio: 0.5
2025-07-15 15:25:05,683 - INFO: Vb_ratio: 0.1
2025-07-15 15:25:05,683 - INFO: true_func: linear
2025-07-15 15:25:05,683 - INFO: mode: S_|_V
2025-07-15 15:25:05,683 - INFO: misspe: poly
2025-07-15 15:25:05,683 - INFO: corr_s: 0.9
2025-07-15 15:25:05,684 - INFO: corr_v: 0.1
2025-07-15 15:25:05,684 - INFO: mms_strength: 1.0
2025-07-15 15:25:05,684 - INFO: spurious: nonlinear
2025-07-15 15:25:05,684 - INFO: r_train: 2.5
2025-07-15 15:25:05,685 - INFO: r_list: [-3, -2, -1.7, -1.5, -1.3, 1.3, 1.5, 1.7, 2, 3]
2025-07-15 15:25:05,685 - INFO: noise_variance: 0.3
2025-07-15 15:25:05,685 - INFO: reweighting: None
2025-07-15 15:25:05,685 - INFO: decorrelation_type: global
2025-07-15 15:25:05,685 - INFO: order: 1
2025-07-15 15:25:05,685 - INFO: iters_balance: 2500
2025-07-15 15:25:05,685 - INFO: topN: 4
2025-07-15 15:25:05,685 - INFO: backend: Weighted_cox
2025-07-15 15:25:05,685 - INFO: paradigm: regr
2025-07-15 15:25:05,685 - INFO: iters_train: 5000
2025-07-15 15:25:05,685 - INFO: lam_backend: 0.21577931514746537
2025-07-15 15:25:05,685 - INFO: lam_backend2: 2.7712399316485526e-05
2025-07-15 15:25:05,685 - INFO: fs_type: STG
2025-07-15 15:25:05,685 - INFO: mask_given: [1, 1, 1, 1, 1, 0, 0, 0, 0, 0]
2025-07-15 15:25:05,685 - INFO: mask_threshold: 0.2
2025-07-15 15:25:05,685 - INFO: lam_STG: 3
2025-07-15 15:25:05,685 - INFO: sigma_STG: 0.1
2025-07-15 15:25:05,685 - INFO: metrics: ['L1_beta_error', 'L2_beta_error']
2025-07-15 15:25:05,685 - INFO: bv_analysis: False
2025-07-15 15:25:05,685 - INFO: seed: 2
2025-07-15 15:25:05,685 - INFO: times: 10
2025-07-15 15:25:05,685 - INFO: result_dir: results
2025-07-15 15:25:05,685 - INFO: n_splits: 5
2025-07-15 15:25:05,685 - INFO: 
2025-07-15 15:25:05,685 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 0 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-15 15:25:05,685 - INFO: --- Starting Round 0 with Seed 2 ---
2025-07-15 15:25:05,716 - INFO: Successfully loaded data from processed_equipment_data.csv. Shape: (598, 187)
2025-07-15 15:25:05,717 - INFO: Data shape after cleaning: (598, 182)
2025-07-15 15:25:05,718 - INFO: Succsessfully get feature columns. Total: 13
2025-07-15 15:25:05,720 - INFO: --- Fold 1/5 ---
2025-07-15 15:25:05,724 - INFO: Features for this fold have been standardized.
2025-07-15 15:25:05,724 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-15 15:25:05,724 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-15 15:25:05,724 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:05,724 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:05,724 - INFO: Starting regression paradigm
2025-07-15 15:25:05,724 - INFO: Training backend model 'Weighted_cox' on fold 1...
2025-07-15 15:25:05,724 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-15 15:25:05,751 - INFO: Fold 1 Training C-index: 0.6150
2025-07-15 15:25:05,756 - INFO: Fold 1 Test C-index: 0.5986
2025-07-15 15:25:05,756 - INFO: Evaluating model on validation set of fold 1...
2025-07-15 15:25:05,760 - INFO: Fold 1 Validation C-index: 0.4870
2025-07-15 15:25:05,760 - INFO: --- Fold 2/5 ---
2025-07-15 15:25:05,763 - INFO: Features for this fold have been standardized.
2025-07-15 15:25:05,763 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-15 15:25:05,763 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-15 15:25:05,763 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:05,763 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:05,763 - INFO: Starting regression paradigm
2025-07-15 15:25:05,763 - INFO: Training backend model 'Weighted_cox' on fold 2...
2025-07-15 15:25:05,763 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-15 15:25:05,790 - INFO: Fold 2 Training C-index: 0.5997
2025-07-15 15:25:05,795 - INFO: Fold 2 Test C-index: 0.6316
2025-07-15 15:25:05,795 - INFO: Evaluating model on validation set of fold 2...
2025-07-15 15:25:05,798 - INFO: Fold 2 Validation C-index: 0.5130
2025-07-15 15:25:05,798 - INFO: --- Fold 3/5 ---
2025-07-15 15:25:05,801 - INFO: Features for this fold have been standardized.
2025-07-15 15:25:05,802 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-15 15:25:05,802 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-15 15:25:05,802 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:05,802 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:05,802 - INFO: Starting regression paradigm
2025-07-15 15:25:05,802 - INFO: Training backend model 'Weighted_cox' on fold 3...
2025-07-15 15:25:05,802 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-15 15:25:05,829 - INFO: Fold 3 Training C-index: 0.6335
2025-07-15 15:25:05,834 - INFO: Fold 3 Test C-index: 0.4737
2025-07-15 15:25:05,834 - INFO: Evaluating model on validation set of fold 3...
2025-07-15 15:25:05,837 - INFO: Fold 3 Validation C-index: 0.4845
2025-07-15 15:25:05,838 - INFO: --- Fold 4/5 ---
2025-07-15 15:25:05,840 - INFO: Features for this fold have been standardized.
2025-07-15 15:25:05,841 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-15 15:25:05,841 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-15 15:25:05,841 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:05,841 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:05,841 - INFO: Starting regression paradigm
2025-07-15 15:25:05,841 - INFO: Training backend model 'Weighted_cox' on fold 4...
2025-07-15 15:25:05,841 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-15 15:25:05,865 - INFO: Fold 4 Training C-index: 0.6281
2025-07-15 15:25:05,870 - INFO: Fold 4 Test C-index: 0.4462
2025-07-15 15:25:05,870 - INFO: Evaluating model on validation set of fold 4...
2025-07-15 15:25:05,873 - INFO: Fold 4 Validation C-index: 0.5130
2025-07-15 15:25:05,873 - INFO: --- Fold 5/5 ---
2025-07-15 15:25:05,876 - INFO: Features for this fold have been standardized.
2025-07-15 15:25:05,876 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-15 15:25:05,876 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-15 15:25:05,876 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:05,877 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:05,877 - INFO: Starting regression paradigm
2025-07-15 15:25:05,877 - INFO: Training backend model 'Weighted_cox' on fold 5...
2025-07-15 15:25:05,877 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-15 15:25:05,903 - INFO: Fold 5 Training C-index: 0.5710
2025-07-15 15:25:05,909 - INFO: Fold 5 Test C-index: 0.6436
2025-07-15 15:25:05,909 - INFO: Evaluating model on validation set of fold 5...
2025-07-15 15:25:05,913 - INFO: Fold 5 Validation C-index: 0.5052
2025-07-15 15:25:05,914 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-15 15:25:05,914 - INFO: C-indices per fold: ['0.4870', '0.5130', '0.4845', '0.5130', '0.5052']
2025-07-15 15:25:05,914 - INFO: Mean C-index: 0.5005
2025-07-15 15:25:05,914 - INFO: Std Dev of C-index: 0.0124
2025-07-15 15:25:05,914 - INFO: Worst C-index: 0.4845
2025-07-15 15:25:05,914 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 1 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-15 15:25:05,914 - INFO: --- Starting Round 1 with Seed 3 ---
2025-07-15 15:25:05,929 - INFO: Successfully loaded data from processed_equipment_data.csv. Shape: (598, 187)
2025-07-15 15:25:05,929 - INFO: Data shape after cleaning: (598, 182)
2025-07-15 15:25:05,930 - INFO: Succsessfully get feature columns. Total: 13
2025-07-15 15:25:05,932 - INFO: --- Fold 1/5 ---
2025-07-15 15:25:05,935 - INFO: Features for this fold have been standardized.
2025-07-15 15:25:05,936 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-15 15:25:05,936 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-15 15:25:05,936 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:05,936 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:05,936 - INFO: Starting regression paradigm
2025-07-15 15:25:05,936 - INFO: Training backend model 'Weighted_cox' on fold 1...
2025-07-15 15:25:05,936 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-15 15:25:05,962 - INFO: Fold 1 Training C-index: 0.6290
2025-07-15 15:25:05,967 - INFO: Fold 1 Test C-index: 0.4504
2025-07-15 15:25:05,967 - INFO: Evaluating model on validation set of fold 1...
2025-07-15 15:25:05,970 - INFO: Fold 1 Validation C-index: 0.5674
2025-07-15 15:25:05,970 - INFO: --- Fold 2/5 ---
2025-07-15 15:25:05,973 - INFO: Features for this fold have been standardized.
2025-07-15 15:25:05,974 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-15 15:25:05,974 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-15 15:25:05,974 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:05,974 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:05,974 - INFO: Starting regression paradigm
2025-07-15 15:25:05,974 - INFO: Training backend model 'Weighted_cox' on fold 2...
2025-07-15 15:25:05,974 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-15 15:25:05,999 - INFO: Fold 2 Training C-index: 0.6207
2025-07-15 15:25:06,004 - INFO: Fold 2 Test C-index: 0.4897
2025-07-15 15:25:06,004 - INFO: Evaluating model on validation set of fold 2...
2025-07-15 15:25:06,008 - INFO: Fold 2 Validation C-index: 0.5512
2025-07-15 15:25:06,008 - INFO: --- Fold 3/5 ---
2025-07-15 15:25:06,010 - INFO: Features for this fold have been standardized.
2025-07-15 15:25:06,011 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-15 15:25:06,011 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-15 15:25:06,011 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:06,011 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:06,011 - INFO: Starting regression paradigm
2025-07-15 15:25:06,011 - INFO: Training backend model 'Weighted_cox' on fold 3...
2025-07-15 15:25:06,011 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-15 15:25:06,036 - INFO: Fold 3 Training C-index: 0.5917
2025-07-15 15:25:06,042 - INFO: Fold 3 Test C-index: 0.5848
2025-07-15 15:25:06,042 - INFO: Evaluating model on validation set of fold 3...
2025-07-15 15:25:06,045 - INFO: Fold 3 Validation C-index: 0.5326
2025-07-15 15:25:06,045 - INFO: --- Fold 4/5 ---
2025-07-15 15:25:06,048 - INFO: Features for this fold have been standardized.
2025-07-15 15:25:06,048 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-15 15:25:06,048 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-15 15:25:06,049 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:06,049 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:06,049 - INFO: Starting regression paradigm
2025-07-15 15:25:06,049 - INFO: Training backend model 'Weighted_cox' on fold 4...
2025-07-15 15:25:06,049 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-15 15:25:06,074 - INFO: Fold 4 Training C-index: 0.6357
2025-07-15 15:25:06,079 - INFO: Fold 4 Test C-index: 0.4701
2025-07-15 15:25:06,079 - INFO: Evaluating model on validation set of fold 4...
2025-07-15 15:25:06,082 - INFO: Fold 4 Validation C-index: 0.5209
2025-07-15 15:25:06,082 - INFO: --- Fold 5/5 ---
2025-07-15 15:25:06,085 - INFO: Features for this fold have been standardized.
2025-07-15 15:25:06,085 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-15 15:25:06,085 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-15 15:25:06,085 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:06,085 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:06,085 - INFO: Starting regression paradigm
2025-07-15 15:25:06,086 - INFO: Training backend model 'Weighted_cox' on fold 5...
2025-07-15 15:25:06,086 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-15 15:25:06,109 - INFO: Fold 5 Training C-index: 0.6080
2025-07-15 15:25:06,115 - INFO: Fold 5 Test C-index: 0.4929
2025-07-15 15:25:06,115 - INFO: Evaluating model on validation set of fold 5...
2025-07-15 15:25:06,118 - INFO: Fold 5 Validation C-index: 0.4907
2025-07-15 15:25:06,119 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-15 15:25:06,119 - INFO: C-indices per fold: ['0.5674', '0.5512', '0.5326', '0.5209', '0.4907']
2025-07-15 15:25:06,119 - INFO: Mean C-index: 0.5326
2025-07-15 15:25:06,119 - INFO: Std Dev of C-index: 0.0263
2025-07-15 15:25:06,119 - INFO: Worst C-index: 0.4907
2025-07-15 15:25:06,119 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 2 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-15 15:25:06,119 - INFO: --- Starting Round 2 with Seed 4 ---
2025-07-15 15:25:06,129 - INFO: Successfully loaded data from processed_equipment_data.csv. Shape: (598, 187)
2025-07-15 15:25:06,130 - INFO: Data shape after cleaning: (598, 182)
2025-07-15 15:25:06,131 - INFO: Succsessfully get feature columns. Total: 13
2025-07-15 15:25:06,132 - INFO: --- Fold 1/5 ---
2025-07-15 15:25:06,135 - INFO: Features for this fold have been standardized.
2025-07-15 15:25:06,135 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-15 15:25:06,135 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-15 15:25:06,135 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:06,135 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:06,135 - INFO: Starting regression paradigm
2025-07-15 15:25:06,135 - INFO: Training backend model 'Weighted_cox' on fold 1...
2025-07-15 15:25:06,135 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-15 15:25:06,161 - INFO: Fold 1 Training C-index: 0.6352
2025-07-15 15:25:06,166 - INFO: Fold 1 Test C-index: 0.4387
2025-07-15 15:25:06,166 - INFO: Evaluating model on validation set of fold 1...
2025-07-15 15:25:06,169 - INFO: Fold 1 Validation C-index: 0.4987
2025-07-15 15:25:06,169 - INFO: --- Fold 2/5 ---
2025-07-15 15:25:06,172 - INFO: Features for this fold have been standardized.
2025-07-15 15:25:06,172 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-15 15:25:06,172 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-15 15:25:06,173 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:06,173 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:06,173 - INFO: Starting regression paradigm
2025-07-15 15:25:06,173 - INFO: Training backend model 'Weighted_cox' on fold 2...
2025-07-15 15:25:06,173 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-15 15:25:06,197 - INFO: Fold 2 Training C-index: 0.6118
2025-07-15 15:25:06,202 - INFO: Fold 2 Test C-index: 0.5465
2025-07-15 15:25:06,202 - INFO: Evaluating model on validation set of fold 2...
2025-07-15 15:25:06,205 - INFO: Fold 2 Validation C-index: 0.5544
2025-07-15 15:25:06,205 - INFO: --- Fold 3/5 ---
2025-07-15 15:25:06,208 - INFO: Features for this fold have been standardized.
2025-07-15 15:25:06,208 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-15 15:25:06,208 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-15 15:25:06,208 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:06,208 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:06,208 - INFO: Starting regression paradigm
2025-07-15 15:25:06,208 - INFO: Training backend model 'Weighted_cox' on fold 3...
2025-07-15 15:25:06,209 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-15 15:25:06,232 - INFO: Fold 3 Training C-index: 0.6257
2025-07-15 15:25:06,237 - INFO: Fold 3 Test C-index: 0.6065
2025-07-15 15:25:06,237 - INFO: Evaluating model on validation set of fold 3...
2025-07-15 15:25:06,240 - INFO: Fold 3 Validation C-index: 0.5305
2025-07-15 15:25:06,240 - INFO: --- Fold 4/5 ---
2025-07-15 15:25:06,243 - INFO: Features for this fold have been standardized.
2025-07-15 15:25:06,243 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-15 15:25:06,243 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-15 15:25:06,243 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:06,244 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:06,244 - INFO: Starting regression paradigm
2025-07-15 15:25:06,244 - INFO: Training backend model 'Weighted_cox' on fold 4...
2025-07-15 15:25:06,244 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-15 15:25:06,268 - INFO: Fold 4 Training C-index: 0.6335
2025-07-15 15:25:06,272 - INFO: Fold 4 Test C-index: 0.4144
2025-07-15 15:25:06,273 - INFO: Evaluating model on validation set of fold 4...
2025-07-15 15:25:06,276 - INFO: Fold 4 Validation C-index: 0.5066
2025-07-15 15:25:06,276 - INFO: --- Fold 5/5 ---
2025-07-15 15:25:06,278 - INFO: Features for this fold have been standardized.
2025-07-15 15:25:06,279 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-15 15:25:06,279 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-15 15:25:06,279 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:06,279 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:06,279 - INFO: Starting regression paradigm
2025-07-15 15:25:06,279 - INFO: Training backend model 'Weighted_cox' on fold 5...
2025-07-15 15:25:06,279 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-15 15:25:06,303 - INFO: Fold 5 Training C-index: 0.6204
2025-07-15 15:25:06,307 - INFO: Fold 5 Test C-index: 0.6357
2025-07-15 15:25:06,307 - INFO: Evaluating model on validation set of fold 5...
2025-07-15 15:25:06,311 - INFO: Fold 5 Validation C-index: 0.5332
2025-07-15 15:25:06,311 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-15 15:25:06,311 - INFO: C-indices per fold: ['0.4987', '0.5544', '0.5305', '0.5066', '0.5332']
2025-07-15 15:25:06,311 - INFO: Mean C-index: 0.5247
2025-07-15 15:25:06,311 - INFO: Std Dev of C-index: 0.0199
2025-07-15 15:25:06,311 - INFO: Worst C-index: 0.4987
2025-07-15 15:25:06,311 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 3 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-15 15:25:06,311 - INFO: --- Starting Round 3 with Seed 5 ---
2025-07-15 15:25:06,323 - INFO: Successfully loaded data from processed_equipment_data.csv. Shape: (598, 187)
2025-07-15 15:25:06,324 - INFO: Data shape after cleaning: (598, 182)
2025-07-15 15:25:06,324 - INFO: Succsessfully get feature columns. Total: 13
2025-07-15 15:25:06,326 - INFO: --- Fold 1/5 ---
2025-07-15 15:25:06,329 - INFO: Features for this fold have been standardized.
2025-07-15 15:25:06,329 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-15 15:25:06,330 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-15 15:25:06,330 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:06,330 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:06,330 - INFO: Starting regression paradigm
2025-07-15 15:25:06,330 - INFO: Training backend model 'Weighted_cox' on fold 1...
2025-07-15 15:25:06,330 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-15 15:25:06,354 - INFO: Fold 1 Training C-index: 0.6764
2025-07-15 15:25:06,359 - INFO: Fold 1 Test C-index: 0.5142
2025-07-15 15:25:06,359 - INFO: Evaluating model on validation set of fold 1...
2025-07-15 15:25:06,363 - INFO: Fold 1 Validation C-index: 0.4235
2025-07-15 15:25:06,363 - INFO: --- Fold 2/5 ---
2025-07-15 15:25:06,366 - INFO: Features for this fold have been standardized.
2025-07-15 15:25:06,366 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-15 15:25:06,366 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-15 15:25:06,366 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:06,366 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:06,366 - INFO: Starting regression paradigm
2025-07-15 15:25:06,366 - INFO: Training backend model 'Weighted_cox' on fold 2...
2025-07-15 15:25:06,366 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-15 15:25:06,391 - INFO: Fold 2 Training C-index: 0.6678
2025-07-15 15:25:06,395 - INFO: Fold 2 Test C-index: 0.5203
2025-07-15 15:25:06,396 - INFO: Evaluating model on validation set of fold 2...
2025-07-15 15:25:06,399 - INFO: Fold 2 Validation C-index: 0.4306
2025-07-15 15:25:06,399 - INFO: --- Fold 3/5 ---
2025-07-15 15:25:06,402 - INFO: Features for this fold have been standardized.
2025-07-15 15:25:06,402 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-15 15:25:06,402 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-15 15:25:06,402 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:06,403 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:06,403 - INFO: Starting regression paradigm
2025-07-15 15:25:06,403 - INFO: Training backend model 'Weighted_cox' on fold 3...
2025-07-15 15:25:06,403 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-15 15:25:06,427 - INFO: Fold 3 Training C-index: 0.6480
2025-07-15 15:25:06,431 - INFO: Fold 3 Test C-index: 0.5412
2025-07-15 15:25:06,431 - INFO: Evaluating model on validation set of fold 3...
2025-07-15 15:25:06,434 - INFO: Fold 3 Validation C-index: 0.4329
2025-07-15 15:25:06,435 - INFO: --- Fold 4/5 ---
2025-07-15 15:25:06,437 - INFO: Features for this fold have been standardized.
2025-07-15 15:25:06,438 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-15 15:25:06,438 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-15 15:25:06,440 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:06,441 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:06,441 - INFO: Starting regression paradigm
2025-07-15 15:25:06,441 - INFO: Training backend model 'Weighted_cox' on fold 4...
2025-07-15 15:25:06,441 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-15 15:25:06,464 - INFO: Fold 4 Training C-index: 0.6378
2025-07-15 15:25:06,469 - INFO: Fold 4 Test C-index: 0.5731
2025-07-15 15:25:06,469 - INFO: Evaluating model on validation set of fold 4...
2025-07-15 15:25:06,472 - INFO: Fold 4 Validation C-index: 0.4776
2025-07-15 15:25:06,472 - INFO: --- Fold 5/5 ---
2025-07-15 15:25:06,475 - INFO: Features for this fold have been standardized.
2025-07-15 15:25:06,475 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-15 15:25:06,475 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-15 15:25:06,476 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:06,476 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:06,476 - INFO: Starting regression paradigm
2025-07-15 15:25:06,476 - INFO: Training backend model 'Weighted_cox' on fold 5...
2025-07-15 15:25:06,476 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-15 15:25:06,499 - INFO: Fold 5 Training C-index: 0.6308
2025-07-15 15:25:06,504 - INFO: Fold 5 Test C-index: 0.6809
2025-07-15 15:25:06,504 - INFO: Evaluating model on validation set of fold 5...
2025-07-15 15:25:06,507 - INFO: Fold 5 Validation C-index: 0.4988
2025-07-15 15:25:06,507 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-15 15:25:06,507 - INFO: C-indices per fold: ['0.4235', '0.4306', '0.4329', '0.4776', '0.4988']
2025-07-15 15:25:06,507 - INFO: Mean C-index: 0.4527
2025-07-15 15:25:06,507 - INFO: Std Dev of C-index: 0.0299
2025-07-15 15:25:06,507 - INFO: Worst C-index: 0.4235
2025-07-15 15:25:06,507 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 4 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-15 15:25:06,507 - INFO: --- Starting Round 4 with Seed 6 ---
2025-07-15 15:25:06,519 - INFO: Successfully loaded data from processed_equipment_data.csv. Shape: (598, 187)
2025-07-15 15:25:06,520 - INFO: Data shape after cleaning: (598, 182)
2025-07-15 15:25:06,520 - INFO: Succsessfully get feature columns. Total: 13
2025-07-15 15:25:06,522 - INFO: --- Fold 1/5 ---
2025-07-15 15:25:06,524 - INFO: Features for this fold have been standardized.
2025-07-15 15:25:06,525 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-15 15:25:06,525 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-15 15:25:06,525 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:06,525 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:06,525 - INFO: Starting regression paradigm
2025-07-15 15:25:06,525 - INFO: Training backend model 'Weighted_cox' on fold 1...
2025-07-15 15:25:06,525 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-15 15:25:06,550 - INFO: Fold 1 Training C-index: 0.6384
2025-07-15 15:25:06,555 - INFO: Fold 1 Test C-index: 0.5298
2025-07-15 15:25:06,555 - INFO: Evaluating model on validation set of fold 1...
2025-07-15 15:25:06,558 - INFO: Fold 1 Validation C-index: 0.5272
2025-07-15 15:25:06,559 - INFO: --- Fold 2/5 ---
2025-07-15 15:25:06,561 - INFO: Features for this fold have been standardized.
2025-07-15 15:25:06,562 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-15 15:25:06,562 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-15 15:25:06,562 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:06,562 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:06,562 - INFO: Starting regression paradigm
2025-07-15 15:25:06,562 - INFO: Training backend model 'Weighted_cox' on fold 2...
2025-07-15 15:25:06,562 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-15 15:25:06,587 - INFO: Fold 2 Training C-index: 0.6180
2025-07-15 15:25:06,592 - INFO: Fold 2 Test C-index: 0.4214
2025-07-15 15:25:06,592 - INFO: Evaluating model on validation set of fold 2...
2025-07-15 15:25:06,595 - INFO: Fold 2 Validation C-index: 0.5248
2025-07-15 15:25:06,595 - INFO: --- Fold 3/5 ---
2025-07-15 15:25:06,598 - INFO: Features for this fold have been standardized.
2025-07-15 15:25:06,598 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-15 15:25:06,599 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-15 15:25:06,599 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:06,599 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:06,599 - INFO: Starting regression paradigm
2025-07-15 15:25:06,599 - INFO: Training backend model 'Weighted_cox' on fold 3...
2025-07-15 15:25:06,599 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-15 15:25:06,623 - INFO: Fold 3 Training C-index: 0.6310
2025-07-15 15:25:06,628 - INFO: Fold 3 Test C-index: 0.5019
2025-07-15 15:25:06,628 - INFO: Evaluating model on validation set of fold 3...
2025-07-15 15:25:06,631 - INFO: Fold 3 Validation C-index: 0.5371
2025-07-15 15:25:06,631 - INFO: --- Fold 4/5 ---
2025-07-15 15:25:06,634 - INFO: Features for this fold have been standardized.
2025-07-15 15:25:06,634 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-15 15:25:06,634 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-15 15:25:06,635 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:06,635 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:06,635 - INFO: Starting regression paradigm
2025-07-15 15:25:06,635 - INFO: Training backend model 'Weighted_cox' on fold 4...
2025-07-15 15:25:06,635 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-15 15:25:06,658 - INFO: Fold 4 Training C-index: 0.6080
2025-07-15 15:25:06,663 - INFO: Fold 4 Test C-index: 0.5038
2025-07-15 15:25:06,663 - INFO: Evaluating model on validation set of fold 4...
2025-07-15 15:25:06,666 - INFO: Fold 4 Validation C-index: 0.5569
2025-07-15 15:25:06,666 - INFO: --- Fold 5/5 ---
2025-07-15 15:25:06,669 - INFO: Features for this fold have been standardized.
2025-07-15 15:25:06,669 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-15 15:25:06,669 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-15 15:25:06,669 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:06,669 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:06,669 - INFO: Starting regression paradigm
2025-07-15 15:25:06,669 - INFO: Training backend model 'Weighted_cox' on fold 5...
2025-07-15 15:25:06,669 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-15 15:25:06,693 - INFO: Fold 5 Training C-index: 0.5811
2025-07-15 15:25:06,697 - INFO: Fold 5 Test C-index: 0.6113
2025-07-15 15:25:06,697 - INFO: Evaluating model on validation set of fold 5...
2025-07-15 15:25:06,700 - INFO: Fold 5 Validation C-index: 0.5297
2025-07-15 15:25:06,700 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-15 15:25:06,700 - INFO: C-indices per fold: ['0.5272', '0.5248', '0.5371', '0.5569', '0.5297']
2025-07-15 15:25:06,700 - INFO: Mean C-index: 0.5351
2025-07-15 15:25:06,700 - INFO: Std Dev of C-index: 0.0117
2025-07-15 15:25:06,700 - INFO: Worst C-index: 0.5248
2025-07-15 15:25:06,700 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 5 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-15 15:25:06,701 - INFO: --- Starting Round 5 with Seed 7 ---
2025-07-15 15:25:06,712 - INFO: Successfully loaded data from processed_equipment_data.csv. Shape: (598, 187)
2025-07-15 15:25:06,712 - INFO: Data shape after cleaning: (598, 182)
2025-07-15 15:25:06,713 - INFO: Succsessfully get feature columns. Total: 13
2025-07-15 15:25:06,715 - INFO: --- Fold 1/5 ---
2025-07-15 15:25:06,717 - INFO: Features for this fold have been standardized.
2025-07-15 15:25:06,718 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-15 15:25:06,718 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-15 15:25:06,718 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:06,718 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:06,718 - INFO: Starting regression paradigm
2025-07-15 15:25:06,718 - INFO: Training backend model 'Weighted_cox' on fold 1...
2025-07-15 15:25:06,718 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-15 15:25:06,742 - INFO: Fold 1 Training C-index: 0.6257
2025-07-15 15:25:06,747 - INFO: Fold 1 Test C-index: 0.4281
2025-07-15 15:25:06,747 - INFO: Evaluating model on validation set of fold 1...
2025-07-15 15:25:06,750 - INFO: Fold 1 Validation C-index: 0.4787
2025-07-15 15:25:06,750 - INFO: --- Fold 2/5 ---
2025-07-15 15:25:06,753 - INFO: Features for this fold have been standardized.
2025-07-15 15:25:06,753 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-15 15:25:06,753 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-15 15:25:06,753 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:06,753 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:06,754 - INFO: Starting regression paradigm
2025-07-15 15:25:06,754 - INFO: Training backend model 'Weighted_cox' on fold 2...
2025-07-15 15:25:06,754 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-15 15:25:06,779 - INFO: Fold 2 Training C-index: 0.6393
2025-07-15 15:25:06,784 - INFO: Fold 2 Test C-index: 0.3654
2025-07-15 15:25:06,784 - INFO: Evaluating model on validation set of fold 2...
2025-07-15 15:25:06,788 - INFO: Fold 2 Validation C-index: 0.4404
2025-07-15 15:25:06,788 - INFO: --- Fold 3/5 ---
2025-07-15 15:25:06,791 - INFO: Features for this fold have been standardized.
2025-07-15 15:25:06,791 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-15 15:25:06,791 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-15 15:25:06,791 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:06,791 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:06,791 - INFO: Starting regression paradigm
2025-07-15 15:25:06,791 - INFO: Training backend model 'Weighted_cox' on fold 3...
2025-07-15 15:25:06,791 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-15 15:25:06,817 - INFO: Fold 3 Training C-index: 0.5822
2025-07-15 15:25:06,822 - INFO: Fold 3 Test C-index: 0.5382
2025-07-15 15:25:06,822 - INFO: Evaluating model on validation set of fold 3...
2025-07-15 15:25:06,825 - INFO: Fold 3 Validation C-index: 0.5820
2025-07-15 15:25:06,825 - INFO: --- Fold 4/5 ---
2025-07-15 15:25:06,828 - INFO: Features for this fold have been standardized.
2025-07-15 15:25:06,828 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-15 15:25:06,828 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-15 15:25:06,828 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:06,828 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:06,828 - INFO: Starting regression paradigm
2025-07-15 15:25:06,829 - INFO: Training backend model 'Weighted_cox' on fold 4...
2025-07-15 15:25:06,829 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-15 15:25:06,852 - INFO: Fold 4 Training C-index: 0.6212
2025-07-15 15:25:06,857 - INFO: Fold 4 Test C-index: 0.3979
2025-07-15 15:25:06,857 - INFO: Evaluating model on validation set of fold 4...
2025-07-15 15:25:06,860 - INFO: Fold 4 Validation C-index: 0.5393
2025-07-15 15:25:06,860 - INFO: --- Fold 5/5 ---
2025-07-15 15:25:06,863 - INFO: Features for this fold have been standardized.
2025-07-15 15:25:06,864 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-15 15:25:06,864 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-15 15:25:06,864 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:06,864 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:06,864 - INFO: Starting regression paradigm
2025-07-15 15:25:06,864 - INFO: Training backend model 'Weighted_cox' on fold 5...
2025-07-15 15:25:06,865 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-15 15:25:06,888 - INFO: Fold 5 Training C-index: 0.5987
2025-07-15 15:25:06,893 - INFO: Fold 5 Test C-index: 0.5314
2025-07-15 15:25:06,893 - INFO: Evaluating model on validation set of fold 5...
2025-07-15 15:25:06,896 - INFO: Fold 5 Validation C-index: 0.5910
2025-07-15 15:25:06,896 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-15 15:25:06,896 - INFO: C-indices per fold: ['0.4787', '0.4404', '0.5820', '0.5393', '0.5910']
2025-07-15 15:25:06,896 - INFO: Mean C-index: 0.5263
2025-07-15 15:25:06,896 - INFO: Std Dev of C-index: 0.0585
2025-07-15 15:25:06,896 - INFO: Worst C-index: 0.4404
2025-07-15 15:25:06,896 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 6 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-15 15:25:06,896 - INFO: --- Starting Round 6 with Seed 8 ---
2025-07-15 15:25:06,908 - INFO: Successfully loaded data from processed_equipment_data.csv. Shape: (598, 187)
2025-07-15 15:25:06,908 - INFO: Data shape after cleaning: (598, 182)
2025-07-15 15:25:06,909 - INFO: Succsessfully get feature columns. Total: 13
2025-07-15 15:25:06,910 - INFO: --- Fold 1/5 ---
2025-07-15 15:25:06,914 - INFO: Features for this fold have been standardized.
2025-07-15 15:25:06,914 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-15 15:25:06,914 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-15 15:25:06,914 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:06,914 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:06,915 - INFO: Starting regression paradigm
2025-07-15 15:25:06,915 - INFO: Training backend model 'Weighted_cox' on fold 1...
2025-07-15 15:25:06,915 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-15 15:25:06,940 - INFO: Fold 1 Training C-index: 0.6276
2025-07-15 15:25:06,945 - INFO: Fold 1 Test C-index: 0.5660
2025-07-15 15:25:06,945 - INFO: Evaluating model on validation set of fold 1...
2025-07-15 15:25:06,948 - INFO: Fold 1 Validation C-index: 0.4251
2025-07-15 15:25:06,948 - INFO: --- Fold 2/5 ---
2025-07-15 15:25:06,951 - INFO: Features for this fold have been standardized.
2025-07-15 15:25:06,951 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-15 15:25:06,951 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-15 15:25:06,951 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:06,952 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:06,952 - INFO: Starting regression paradigm
2025-07-15 15:25:06,952 - INFO: Training backend model 'Weighted_cox' on fold 2...
2025-07-15 15:25:06,952 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-15 15:25:06,977 - INFO: Fold 2 Training C-index: 0.6576
2025-07-15 15:25:06,981 - INFO: Fold 2 Test C-index: 0.4060
2025-07-15 15:25:06,981 - INFO: Evaluating model on validation set of fold 2...
2025-07-15 15:25:06,985 - INFO: Fold 2 Validation C-index: 0.4565
2025-07-15 15:25:06,985 - INFO: --- Fold 3/5 ---
2025-07-15 15:25:06,988 - INFO: Features for this fold have been standardized.
2025-07-15 15:25:06,988 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-15 15:25:06,988 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-15 15:25:06,988 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:06,988 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:06,988 - INFO: Starting regression paradigm
2025-07-15 15:25:06,988 - INFO: Training backend model 'Weighted_cox' on fold 3...
2025-07-15 15:25:06,988 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-15 15:25:07,014 - INFO: Fold 3 Training C-index: 0.6008
2025-07-15 15:25:07,019 - INFO: Fold 3 Test C-index: 0.6692
2025-07-15 15:25:07,020 - INFO: Evaluating model on validation set of fold 3...
2025-07-15 15:25:07,023 - INFO: Fold 3 Validation C-index: 0.4710
2025-07-15 15:25:07,023 - INFO: --- Fold 4/5 ---
2025-07-15 15:25:07,026 - INFO: Features for this fold have been standardized.
2025-07-15 15:25:07,026 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-15 15:25:07,026 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-15 15:25:07,026 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:07,026 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:07,026 - INFO: Starting regression paradigm
2025-07-15 15:25:07,026 - INFO: Training backend model 'Weighted_cox' on fold 4...
2025-07-15 15:25:07,026 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-15 15:25:07,051 - INFO: Fold 4 Training C-index: 0.6581
2025-07-15 15:25:07,056 - INFO: Fold 4 Test C-index: 0.4790
2025-07-15 15:25:07,056 - INFO: Evaluating model on validation set of fold 4...
2025-07-15 15:25:07,059 - INFO: Fold 4 Validation C-index: 0.5217
2025-07-15 15:25:07,059 - INFO: --- Fold 5/5 ---
2025-07-15 15:25:07,062 - INFO: Features for this fold have been standardized.
2025-07-15 15:25:07,062 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-15 15:25:07,062 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-15 15:25:07,063 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:07,063 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:07,063 - INFO: Starting regression paradigm
2025-07-15 15:25:07,063 - INFO: Training backend model 'Weighted_cox' on fold 5...
2025-07-15 15:25:07,063 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-15 15:25:07,086 - INFO: Fold 5 Training C-index: 0.6258
2025-07-15 15:25:07,091 - INFO: Fold 5 Test C-index: 0.6105
2025-07-15 15:25:07,091 - INFO: Evaluating model on validation set of fold 5...
2025-07-15 15:25:07,094 - INFO: Fold 5 Validation C-index: 0.4928
2025-07-15 15:25:07,094 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-15 15:25:07,094 - INFO: C-indices per fold: ['0.4251', '0.4565', '0.4710', '0.5217', '0.4928']
2025-07-15 15:25:07,094 - INFO: Mean C-index: 0.4734
2025-07-15 15:25:07,094 - INFO: Std Dev of C-index: 0.0327
2025-07-15 15:25:07,094 - INFO: Worst C-index: 0.4251
2025-07-15 15:25:07,095 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 7 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-15 15:25:07,095 - INFO: --- Starting Round 7 with Seed 9 ---
2025-07-15 15:25:07,106 - INFO: Successfully loaded data from processed_equipment_data.csv. Shape: (598, 187)
2025-07-15 15:25:07,106 - INFO: Data shape after cleaning: (598, 182)
2025-07-15 15:25:07,107 - INFO: Succsessfully get feature columns. Total: 13
2025-07-15 15:25:07,108 - INFO: --- Fold 1/5 ---
2025-07-15 15:25:07,111 - INFO: Features for this fold have been standardized.
2025-07-15 15:25:07,111 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-15 15:25:07,111 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-15 15:25:07,111 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:07,111 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:07,111 - INFO: Starting regression paradigm
2025-07-15 15:25:07,111 - INFO: Training backend model 'Weighted_cox' on fold 1...
2025-07-15 15:25:07,111 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-15 15:25:07,135 - INFO: Fold 1 Training C-index: 0.5933
2025-07-15 15:25:07,140 - INFO: Fold 1 Test C-index: 0.5719
2025-07-15 15:25:07,140 - INFO: Evaluating model on validation set of fold 1...
2025-07-15 15:25:07,143 - INFO: Fold 1 Validation C-index: 0.6206
2025-07-15 15:25:07,143 - INFO: --- Fold 2/5 ---
2025-07-15 15:25:07,145 - INFO: Features for this fold have been standardized.
2025-07-15 15:25:07,146 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-15 15:25:07,146 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-15 15:25:07,146 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:07,146 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:07,146 - INFO: Starting regression paradigm
2025-07-15 15:25:07,146 - INFO: Training backend model 'Weighted_cox' on fold 2...
2025-07-15 15:25:07,146 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-15 15:25:07,171 - INFO: Fold 2 Training C-index: 0.6079
2025-07-15 15:25:07,176 - INFO: Fold 2 Test C-index: 0.5149
2025-07-15 15:25:07,176 - INFO: Evaluating model on validation set of fold 2...
2025-07-15 15:25:07,179 - INFO: Fold 2 Validation C-index: 0.5854
2025-07-15 15:25:07,179 - INFO: --- Fold 3/5 ---
2025-07-15 15:25:07,182 - INFO: Features for this fold have been standardized.
2025-07-15 15:25:07,182 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-15 15:25:07,182 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-15 15:25:07,182 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:07,182 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:07,182 - INFO: Starting regression paradigm
2025-07-15 15:25:07,182 - INFO: Training backend model 'Weighted_cox' on fold 3...
2025-07-15 15:25:07,182 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-15 15:25:07,207 - INFO: Fold 3 Training C-index: 0.5842
2025-07-15 15:25:07,212 - INFO: Fold 3 Test C-index: 0.5157
2025-07-15 15:25:07,212 - INFO: Evaluating model on validation set of fold 3...
2025-07-15 15:25:07,215 - INFO: Fold 3 Validation C-index: 0.6432
2025-07-15 15:25:07,215 - INFO: --- Fold 4/5 ---
2025-07-15 15:25:07,218 - INFO: Features for this fold have been standardized.
2025-07-15 15:25:07,218 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-15 15:25:07,218 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-15 15:25:07,218 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:07,218 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:07,218 - INFO: Starting regression paradigm
2025-07-15 15:25:07,218 - INFO: Training backend model 'Weighted_cox' on fold 4...
2025-07-15 15:25:07,218 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-15 15:25:07,242 - INFO: Fold 4 Training C-index: 0.6186
2025-07-15 15:25:07,247 - INFO: Fold 4 Test C-index: 0.5150
2025-07-15 15:25:07,247 - INFO: Evaluating model on validation set of fold 4...
2025-07-15 15:25:07,251 - INFO: Fold 4 Validation C-index: 0.5829
2025-07-15 15:25:07,251 - INFO: --- Fold 5/5 ---
2025-07-15 15:25:07,254 - INFO: Features for this fold have been standardized.
2025-07-15 15:25:07,254 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-15 15:25:07,254 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-15 15:25:07,254 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:07,254 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:07,254 - INFO: Starting regression paradigm
2025-07-15 15:25:07,254 - INFO: Training backend model 'Weighted_cox' on fold 5...
2025-07-15 15:25:07,254 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-15 15:25:07,279 - INFO: Fold 5 Training C-index: 0.5959
2025-07-15 15:25:07,283 - INFO: Fold 5 Test C-index: 0.5336
2025-07-15 15:25:07,283 - INFO: Evaluating model on validation set of fold 5...
2025-07-15 15:25:07,287 - INFO: Fold 5 Validation C-index: 0.6080
2025-07-15 15:25:07,287 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-15 15:25:07,287 - INFO: C-indices per fold: ['0.6206', '0.5854', '0.6432', '0.5829', '0.6080']
2025-07-15 15:25:07,287 - INFO: Mean C-index: 0.6080
2025-07-15 15:25:07,287 - INFO: Std Dev of C-index: 0.0225
2025-07-15 15:25:07,287 - INFO: Worst C-index: 0.5829
2025-07-15 15:25:07,287 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 8 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-15 15:25:07,287 - INFO: --- Starting Round 8 with Seed 10 ---
2025-07-15 15:25:07,298 - INFO: Successfully loaded data from processed_equipment_data.csv. Shape: (598, 187)
2025-07-15 15:25:07,299 - INFO: Data shape after cleaning: (598, 182)
2025-07-15 15:25:07,299 - INFO: Succsessfully get feature columns. Total: 13
2025-07-15 15:25:07,301 - INFO: --- Fold 1/5 ---
2025-07-15 15:25:07,304 - INFO: Features for this fold have been standardized.
2025-07-15 15:25:07,304 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-15 15:25:07,304 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-15 15:25:07,304 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:07,304 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:07,304 - INFO: Starting regression paradigm
2025-07-15 15:25:07,304 - INFO: Training backend model 'Weighted_cox' on fold 1...
2025-07-15 15:25:07,304 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-15 15:25:07,328 - INFO: Fold 1 Training C-index: 0.6221
2025-07-15 15:25:07,332 - INFO: Fold 1 Test C-index: 0.5455
2025-07-15 15:25:07,332 - INFO: Evaluating model on validation set of fold 1...
2025-07-15 15:25:07,335 - INFO: Fold 1 Validation C-index: 0.5241
2025-07-15 15:25:07,336 - INFO: --- Fold 2/5 ---
2025-07-15 15:25:07,338 - INFO: Features for this fold have been standardized.
2025-07-15 15:25:07,338 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-15 15:25:07,339 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-15 15:25:07,339 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:07,339 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:07,339 - INFO: Starting regression paradigm
2025-07-15 15:25:07,339 - INFO: Training backend model 'Weighted_cox' on fold 2...
2025-07-15 15:25:07,339 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-15 15:25:07,362 - INFO: Fold 2 Training C-index: 0.6151
2025-07-15 15:25:07,367 - INFO: Fold 2 Test C-index: 0.5511
2025-07-15 15:25:07,367 - INFO: Evaluating model on validation set of fold 2...
2025-07-15 15:25:07,370 - INFO: Fold 2 Validation C-index: 0.5195
2025-07-15 15:25:07,370 - INFO: --- Fold 3/5 ---
2025-07-15 15:25:07,372 - INFO: Features for this fold have been standardized.
2025-07-15 15:25:07,373 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-15 15:25:07,373 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-15 15:25:07,373 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:07,373 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:07,373 - INFO: Starting regression paradigm
2025-07-15 15:25:07,373 - INFO: Training backend model 'Weighted_cox' on fold 3...
2025-07-15 15:25:07,373 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-15 15:25:07,397 - INFO: Fold 3 Training C-index: 0.5882
2025-07-15 15:25:07,402 - INFO: Fold 3 Test C-index: 0.5939
2025-07-15 15:25:07,402 - INFO: Evaluating model on validation set of fold 3...
2025-07-15 15:25:07,405 - INFO: Fold 3 Validation C-index: 0.4529
2025-07-15 15:25:07,405 - INFO: --- Fold 4/5 ---
2025-07-15 15:25:07,408 - INFO: Features for this fold have been standardized.
2025-07-15 15:25:07,408 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-15 15:25:07,408 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-15 15:25:07,408 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:07,408 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:07,408 - INFO: Starting regression paradigm
2025-07-15 15:25:07,408 - INFO: Training backend model 'Weighted_cox' on fold 4...
2025-07-15 15:25:07,408 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-15 15:25:07,434 - INFO: Fold 4 Training C-index: 0.6481
2025-07-15 15:25:07,439 - INFO: Fold 4 Test C-index: 0.3764
2025-07-15 15:25:07,439 - INFO: Evaluating model on validation set of fold 4...
2025-07-15 15:25:07,442 - INFO: Fold 4 Validation C-index: 0.3747
2025-07-15 15:25:07,442 - INFO: --- Fold 5/5 ---
2025-07-15 15:25:07,445 - INFO: Features for this fold have been standardized.
2025-07-15 15:25:07,445 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-15 15:25:07,445 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-15 15:25:07,445 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:07,445 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:07,445 - INFO: Starting regression paradigm
2025-07-15 15:25:07,445 - INFO: Training backend model 'Weighted_cox' on fold 5...
2025-07-15 15:25:07,445 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-15 15:25:07,470 - INFO: Fold 5 Training C-index: 0.6044
2025-07-15 15:25:07,474 - INFO: Fold 5 Test C-index: 0.4564
2025-07-15 15:25:07,474 - INFO: Evaluating model on validation set of fold 5...
2025-07-15 15:25:07,478 - INFO: Fold 5 Validation C-index: 0.5310
2025-07-15 15:25:07,478 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-15 15:25:07,478 - INFO: C-indices per fold: ['0.5241', '0.5195', '0.4529', '0.3747', '0.5310']
2025-07-15 15:25:07,478 - INFO: Mean C-index: 0.4805
2025-07-15 15:25:07,478 - INFO: Std Dev of C-index: 0.0599
2025-07-15 15:25:07,478 - INFO: Worst C-index: 0.3747
2025-07-15 15:25:07,478 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 9 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-15 15:25:07,478 - INFO: --- Starting Round 9 with Seed 11 ---
2025-07-15 15:25:07,489 - INFO: Successfully loaded data from processed_equipment_data.csv. Shape: (598, 187)
2025-07-15 15:25:07,490 - INFO: Data shape after cleaning: (598, 182)
2025-07-15 15:25:07,490 - INFO: Succsessfully get feature columns. Total: 13
2025-07-15 15:25:07,492 - INFO: --- Fold 1/5 ---
2025-07-15 15:25:07,495 - INFO: Features for this fold have been standardized.
2025-07-15 15:25:07,495 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-15 15:25:07,495 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-15 15:25:07,495 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:07,495 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:07,495 - INFO: Starting regression paradigm
2025-07-15 15:25:07,495 - INFO: Training backend model 'Weighted_cox' on fold 1...
2025-07-15 15:25:07,495 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-15 15:25:07,520 - INFO: Fold 1 Training C-index: 0.6287
2025-07-15 15:25:07,524 - INFO: Fold 1 Test C-index: 0.4713
2025-07-15 15:25:07,524 - INFO: Evaluating model on validation set of fold 1...
2025-07-15 15:25:07,527 - INFO: Fold 1 Validation C-index: 0.5678
2025-07-15 15:25:07,527 - INFO: --- Fold 2/5 ---
2025-07-15 15:25:07,530 - INFO: Features for this fold have been standardized.
2025-07-15 15:25:07,530 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-15 15:25:07,531 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-15 15:25:07,531 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:07,531 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:07,531 - INFO: Starting regression paradigm
2025-07-15 15:25:07,531 - INFO: Training backend model 'Weighted_cox' on fold 2...
2025-07-15 15:25:07,531 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-15 15:25:07,554 - INFO: Fold 2 Training C-index: 0.6068
2025-07-15 15:25:07,559 - INFO: Fold 2 Test C-index: 0.6000
2025-07-15 15:25:07,559 - INFO: Evaluating model on validation set of fold 2...
2025-07-15 15:25:07,562 - INFO: Fold 2 Validation C-index: 0.5075
2025-07-15 15:25:07,562 - INFO: --- Fold 3/5 ---
2025-07-15 15:25:07,565 - INFO: Features for this fold have been standardized.
2025-07-15 15:25:07,565 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-15 15:25:07,565 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-15 15:25:07,565 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:07,565 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:07,565 - INFO: Starting regression paradigm
2025-07-15 15:25:07,565 - INFO: Training backend model 'Weighted_cox' on fold 3...
2025-07-15 15:25:07,565 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-15 15:25:07,588 - INFO: Fold 3 Training C-index: 0.6348
2025-07-15 15:25:07,593 - INFO: Fold 3 Test C-index: 0.4982
2025-07-15 15:25:07,593 - INFO: Evaluating model on validation set of fold 3...
2025-07-15 15:25:07,596 - INFO: Fold 3 Validation C-index: 0.5000
2025-07-15 15:25:07,596 - INFO: --- Fold 4/5 ---
2025-07-15 15:25:07,599 - INFO: Features for this fold have been standardized.
2025-07-15 15:25:07,599 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-15 15:25:07,599 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-15 15:25:07,599 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:07,599 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:07,599 - INFO: Starting regression paradigm
2025-07-15 15:25:07,599 - INFO: Training backend model 'Weighted_cox' on fold 4...
2025-07-15 15:25:07,599 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-15 15:25:07,624 - INFO: Fold 4 Training C-index: 0.6023
2025-07-15 15:25:07,629 - INFO: Fold 4 Test C-index: 0.5833
2025-07-15 15:25:07,629 - INFO: Evaluating model on validation set of fold 4...
2025-07-15 15:25:07,632 - INFO: Fold 4 Validation C-index: 0.5126
2025-07-15 15:25:07,632 - INFO: --- Fold 5/5 ---
2025-07-15 15:25:07,635 - INFO: Features for this fold have been standardized.
2025-07-15 15:25:07,635 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-15 15:25:07,635 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-15 15:25:07,635 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:07,636 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-15 15:25:07,636 - INFO: Starting regression paradigm
2025-07-15 15:25:07,636 - INFO: Training backend model 'Weighted_cox' on fold 5...
2025-07-15 15:25:07,636 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-15 15:25:07,661 - INFO: Fold 5 Training C-index: 0.6160
2025-07-15 15:25:07,666 - INFO: Fold 5 Test C-index: 0.5000
2025-07-15 15:25:07,666 - INFO: Evaluating model on validation set of fold 5...
2025-07-15 15:25:07,669 - INFO: Fold 5 Validation C-index: 0.5452
2025-07-15 15:25:07,669 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-15 15:25:07,669 - INFO: C-indices per fold: ['0.5678', '0.5075', '0.5000', '0.5126', '0.5452']
2025-07-15 15:25:07,669 - INFO: Mean C-index: 0.5266
2025-07-15 15:25:07,669 - INFO: Std Dev of C-index: 0.0258
2025-07-15 15:25:07,669 - INFO: Worst C-index: 0.5000
2025-07-15 15:25:07,670 - INFO: ==================================================
2025-07-15 15:25:07,670 - INFO: Final Summary Over 10 Independent Run(s), validation set (GEHC's data)
2025-07-15 15:25:07,670 - INFO: ==================================================
2025-07-15 15:25:07,670 - INFO: Average of 'mean_c_index' over 10 runs: 0.5160 (Std Dev: 0.0409)
2025-07-15 15:25:07,670 - INFO: Average of 'std_c_index' over 10 runs: 0.0300 (Std Dev: 0.0160)
2025-07-15 15:25:07,670 - INFO: Average of 'worst_c_index' over 10 runs: 0.4745 (Std Dev: 0.0566)
2025-07-15 15:25:07,670 - INFO: ==================================================
2025-07-15 15:25:07,670 - INFO: Final Summary Over 10 Independent Run(s), test set (GEHC's generated data)
2025-07-15 15:25:07,670 - INFO: ==================================================
2025-07-15 15:25:07,670 - INFO: Average of 'mean_c_index_test' over 10 runs: 0.5228 (Std Dev: 0.0314)
2025-07-15 15:25:07,670 - INFO: Average of 'std_c_index_test' over 10 runs: 0.0654 (Std Dev: 0.0207)
2025-07-15 15:25:07,670 - INFO: Average of 'worst_c_index_test' over 10 runs: 0.4381 (Std Dev: 0.0490)
