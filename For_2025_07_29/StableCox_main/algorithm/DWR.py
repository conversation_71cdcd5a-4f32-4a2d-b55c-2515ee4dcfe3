import torch
from torch import optim
import numpy as np
from utils import weighted_cov_torch

def decorr_loss(X, weight, cov_mask=None, order=1):
    """
    这里的 cov_mask 其实就是在介绍哪些协方差需要被处理
    :param X:
    :param weight:
    :param cov_mask:
    :param order:
    :return:
    """
    n = X.shape[0]
    p = X.shape[1]
    balance_loss = 0.0 
    for a in range(1, order+1):
        for b in range(a, order+1):
            if a != b:
                cov_mat = weighted_cov_torch(X**a, X**b, W=weight**2/n)
            else:
                cov_mat = weighted_cov_torch(X**a, W=weight**2/n)
            cov_mat = cov_mat**2 # 将协方差矩阵中的所有元素平方，使得惩罚项都为正，且放大较大协方差的影响
            cov_mat = cov_mat * cov_mask # 应用掩码，如果 cov_mask[k,l]=0，则对应的平方协方差项归零，不计入损失
            # 3. 计算当前 (a,b) 组合下的 balance_loss贡献
            #    目标是减小不同特征之间的协方差（即协方差矩阵的非对角线元素）
            #    torch.sum(cov_mat, dim=1): 对每行（每个特征k）求和，得到 sum_l (Cov_W(X_k^a, X_l^b))^2
            #    torch.diag(cov_mat): 提取对角线元素，即 (Cov_W(X_k^a, X_k^b))^2 (自身方差或与同幂次自身的协方差)
            #    sum_off_diagonal_sq_cov = torch.sum(cov_mat, dim=1) - torch.diag(cov_mat)
            #        这计算了每个特征k与其他所有特征l (l≠k) 的平方协方差之和。
            #    torch.sqrt(sum_off_diagonal_sq_cov + 1e-10): 对上述和开方（epsilon用于数值稳定）
            #    torch.sum(...): 将所有特征的这个开方和加起来，作为这一对(a,b)的 balance_loss 贡献
            balance_loss += torch.sum(torch.sqrt(torch.sum(cov_mat, dim=1)-torch.diag(cov_mat) +  1e-10))

    loss_weight_sum = (torch.sum(weight * weight) - n) ** 2
    loss_weight_l2 = torch.sum((weight * weight) ** 2)
    loss = 2000.0 / p * balance_loss + 100 * loss_weight_sum + 0.0005 * loss_weight_l2 # hard coding # 这些系数 (2000.0/p, 100, 0.0005) 是硬编码的，用于平衡各项的重要性
    return loss, balance_loss, loss_weight_sum, loss_weight_l2

def DWR(X, cov_mask=None, order=1, num_steps = 5000, lr = 0.01, tol=1e-8, loss_lb=0.001, iter_print=500, logger=None, device=None):
    X = torch.tensor(X, dtype=torch.float, device=device)
    n, p = X.shape    
    
    if cov_mask is None:
        cov_mask = torch.ones((p, p), device=device)
    else:
        cov_mask = torch.tensor(cov_mask, dtype=torch.float, device=device)

    weight = torch.ones(n, 1, device=device)
    weight = weight.to(device)
    weight.requires_grad = True
    optimizer = optim.Adam([weight,], lr = lr)
    
    loss_prev = 0.0
    for i in range(num_steps):
        optimizer.zero_grad()
        loss, balance_loss, loss_s, loss_2 = decorr_loss(X, weight, cov_mask, order=order)
        loss.backward()
        optimizer.step()
        if abs(loss-loss_prev) <= tol or balance_loss <= loss_lb:
            break
        if (i+1) % iter_print == 0:
            logger.debug('iter %d: decorrelate loss %.6f balance loss %.6f loss_s %.6f  loss_l2 %.6f' % (i+1, loss, balance_loss, loss_s, loss_2))
    weight = (weight**2).cpu().detach().numpy()
    weight /= np.sum(weight) # normalize: weights sum up to 1
    return weight
