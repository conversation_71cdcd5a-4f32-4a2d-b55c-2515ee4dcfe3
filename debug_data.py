import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, StratifiedKFold

# 加载数据
df = pd.read_csv('processed_equipment_data_new.csv')
print('原始数据形状:', df.shape)

# 移除SN_Common列和问题列
if 'SN_Common' in df.columns:
    df = df.drop(columns=['SN_Common'])
problematic_cols = ['0A_Flattop', 'Ambient_HST_delta', 'Ambient_temp', 'Offset_cal']
cols_to_drop = [col for col in problematic_cols if col in df.columns]
df = df.drop(columns=cols_to_drop)

feature_cols = [col for col in df.columns if col not in ['duration', 'event']]
print('特征列数量:', len(feature_cols))

# 数据分割
df_trainval, df_test = train_test_split(
    df, 
    test_size=0.1, 
    stratify=df['event'],
    random_state=42
)

print('训练验证集形状:', df_trainval.shape)
print('测试集形状:', df_test.shape)

# 交叉验证测试
cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)

for fold_idx, (train_idx, val_idx) in enumerate(cv.split(df_trainval, df_trainval['event'])):
    train_fold = df_trainval.iloc[train_idx]
    val_fold = df_trainval.iloc[val_idx]
    
    print(f'Fold {fold_idx}:')
    print(f'  训练集大小: {len(train_fold)}, 事件数: {train_fold["event"].sum()}')
    print(f'  验证集大小: {len(val_fold)}, 事件数: {val_fold["event"].sum()}')
    
    # 检查是否有足够的事件对比
    train_events = train_fold['event'].sum()
    val_events = val_fold['event'].sum()
    
    if train_events == 0 or train_events == len(train_fold):
        print(f'  警告: 训练集只有一种结果类型')
    if val_events == 0 or val_events == len(val_fold):
        print(f'  警告: 验证集只有一种结果类型')
        
    # 检查duration的分布
    train_durations = train_fold['duration'].values
    val_durations = val_fold['duration'].values
    
    print(f'  训练集duration范围: {train_durations.min():.0f} - {train_durations.max():.0f}')
    print(f'  验证集duration范围: {val_durations.min():.0f} - {val_durations.max():.0f}')
    
    # 检查是否有相同的duration值（这可能导致ties问题）
    train_unique_durations = len(np.unique(train_durations))
    val_unique_durations = len(np.unique(val_durations))
    
    print(f'  训练集唯一duration数: {train_unique_durations}/{len(train_fold)}')
    print(f'  验证集唯一duration数: {val_unique_durations}/{len(val_fold)}')
    print()

# 测试STG数据准备
print("=" * 50)
print("测试STG数据准备")

# 模拟STG数据准备过程
try:
    from stg import STG
    import stg.utils as stg_utils
    
    # 取一个小样本进行测试
    sample_df = df_trainval.head(50)
    
    # 准备数据
    X = sample_df[feature_cols].to_numpy()
    y = {'e': sample_df['event'].to_numpy(), 't': sample_df['duration'].to_numpy()}
    
    print(f'样本数据形状: X={X.shape}, e={len(y["e"])}, t={len(y["t"])}')
    print(f'事件分布: {np.bincount(y["e"])}')
    print(f'Duration范围: {y["t"].min()} - {y["t"].max()}')
    
    # 调用STG的prepare_data
    X_prepared, E_prepared, T_prepared = stg_utils.prepare_data(X, y)
    
    print(f'STG准备后数据形状: X={X_prepared.shape}, E={E_prepared.shape}, T={T_prepared.shape}')
    print(f'准备后事件分布: {np.bincount(E_prepared.astype(int))}')
    print(f'准备后Duration范围: {T_prepared.min()} - {T_prepared.max()}')
    
except ImportError:
    print("STG包未安装，跳过STG测试")
except Exception as e:
    print(f"STG测试出错: {e}")
