2025-07-22 09:51:00,619 - INFO: p: 10
2025-07-22 09:51:00,619 - INFO: n: 2000
2025-07-22 09:51:00,619 - INFO: V_ratio: 0.5
2025-07-22 09:51:00,619 - INFO: Vb_ratio: 0.1
2025-07-22 09:51:00,619 - INFO: true_func: linear
2025-07-22 09:51:00,619 - INFO: mode: S_|_V
2025-07-22 09:51:00,619 - INFO: misspe: poly
2025-07-22 09:51:00,621 - INFO: corr_s: 0.9
2025-07-22 09:51:00,621 - INFO: corr_v: 0.1
2025-07-22 09:51:00,621 - INFO: mms_strength: 1.0
2025-07-22 09:51:00,621 - INFO: spurious: nonlinear
2025-07-22 09:51:00,621 - INFO: r_train: 2.5
2025-07-22 09:51:00,621 - INFO: r_list: [-3, -2, -1.7, -1.5, -1.3, 1.3, 1.5, 1.7, 2, 3]
2025-07-22 09:51:00,621 - INFO: noise_variance: 0.3
2025-07-22 09:51:00,621 - INFO: reweighting: SRDO
2025-07-22 09:51:00,621 - INFO: decorrelation_type: global
2025-07-22 09:51:00,621 - INFO: order: 1
2025-07-22 09:51:00,621 - INFO: iters_balance: 2500
2025-07-22 09:51:00,621 - INFO: topN: 10
2025-07-22 09:51:00,621 - INFO: backend: LogNormal
2025-07-22 09:51:00,621 - INFO: paradigm: regr
2025-07-22 09:51:00,621 - INFO: iters_train: 5000
2025-07-22 09:51:00,621 - INFO: lam_backend: 0.03
2025-07-22 09:51:00,621 - INFO: lam_backend2: 0.03
2025-07-22 09:51:00,621 - INFO: fs_type: STG
2025-07-22 09:51:00,621 - INFO: mask_given: [1, 1, 1, 1, 1, 0, 0, 0, 0, 0]
2025-07-22 09:51:00,621 - INFO: mask_threshold: 0.2
2025-07-22 09:51:00,621 - INFO: lam_STG: 3
2025-07-22 09:51:00,621 - INFO: sigma_STG: 0.1
2025-07-22 09:51:00,621 - INFO: metrics: ['L1_beta_error', 'L2_beta_error']
2025-07-22 09:51:00,621 - INFO: bv_analysis: False
2025-07-22 09:51:00,622 - INFO: seed: 2
2025-07-22 09:51:00,622 - INFO: times: 10
2025-07-22 09:51:00,622 - INFO: result_dir: results
2025-07-22 09:51:00,622 - INFO: n_splits: 10
2025-07-22 09:51:00,622 - INFO: 
2025-07-22 09:51:00,622 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 0 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-22 09:51:00,622 - INFO: --- Starting Round 0 with Seed 2 ---
2025-07-22 09:51:00,666 - INFO: Successfully loaded data from processed_equipment_data_new.csv. Shape: (598, 187)
2025-07-22 09:51:00,667 - INFO: Succsessfully get feature columns. Total: 180
2025-07-22 09:51:00,671 - INFO: --- Fold 1/10 ---
2025-07-22 09:51:00,693 - INFO: Features for this fold have been standardized.
2025-07-22 09:51:00,696 - INFO: Using SRDO reweighting
2025-07-22 09:51:01,395 - INFO: SRDO reweighting completed.
2025-07-22 09:51:01,396 - INFO: Weights calculated and normalized for fold 1.
2025-07-22 09:51:01,396 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.015916
2025-07-22 09:51:01,396 - INFO: After normalization - max: 7.703530, min: 0.000000
2025-07-22 09:51:01,396 - INFO: W before clip: min=0.000000, max=7.703530, mean=1.000000, std=1.217590
2025-07-22 09:51:01,396 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.890253, std=0.948658
2025-07-22 09:51:01,396 - INFO: Starting regression paradigm
2025-07-22 09:51:01,396 - INFO: Training backend model 'LogNormal' on fold 1...
2025-07-22 09:51:01,396 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:51:02,202 - INFO: Fold 1 Training C-index: 0.5727
2025-07-22 09:51:02,483 - INFO: Fold 1 Test C-index: 0.5529
2025-07-22 09:51:02,483 - INFO: Fold 1: New best CV model with fold validation C-index: 0.5426
2025-07-22 09:51:02,483 - INFO: --- Fold 2/10 ---
2025-07-22 09:51:02,489 - INFO: Features for this fold have been standardized.
2025-07-22 09:51:02,490 - INFO: Using SRDO reweighting
2025-07-22 09:51:06,381 - INFO: SRDO reweighting completed.
2025-07-22 09:51:06,381 - INFO: Weights calculated and normalized for fold 2.
2025-07-22 09:51:06,381 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.014846
2025-07-22 09:51:06,381 - INFO: After normalization - max: 7.185308, min: 0.000000
2025-07-22 09:51:06,381 - INFO: W before clip: min=0.000000, max=7.185308, mean=1.000000, std=1.354644
2025-07-22 09:51:06,381 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.827428, std=0.906521
2025-07-22 09:51:06,381 - INFO: Starting regression paradigm
2025-07-22 09:51:06,381 - INFO: Training backend model 'LogNormal' on fold 2...
2025-07-22 09:51:06,381 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:51:06,630 - INFO: Fold 2 Training C-index: 0.5278
2025-07-22 09:51:06,891 - INFO: Fold 2 Test C-index: 0.3974
2025-07-22 09:51:06,891 - INFO: --- Fold 3/10 ---
2025-07-22 09:51:06,895 - INFO: Features for this fold have been standardized.
2025-07-22 09:51:06,896 - INFO: Using SRDO reweighting
2025-07-22 09:51:11,676 - INFO: SRDO reweighting completed.
2025-07-22 09:51:11,676 - INFO: Weights calculated and normalized for fold 3.
2025-07-22 09:51:11,677 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.024795
2025-07-22 09:51:11,677 - INFO: After normalization - max: 12.000723, min: 0.000000
2025-07-22 09:51:11,677 - INFO: W before clip: min=0.000000, max=12.000723, mean=1.000000, std=1.469940
2025-07-22 09:51:11,677 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.800973, std=0.900117
2025-07-22 09:51:11,677 - INFO: Starting regression paradigm
2025-07-22 09:51:11,677 - INFO: Training backend model 'LogNormal' on fold 3...
2025-07-22 09:51:11,677 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:51:11,872 - INFO: Fold 3 Training C-index: 0.5282
2025-07-22 09:51:12,084 - INFO: Fold 3 Test C-index: 0.3537
2025-07-22 09:51:12,084 - INFO: --- Fold 4/10 ---
2025-07-22 09:51:12,087 - INFO: Features for this fold have been standardized.
2025-07-22 09:51:12,087 - INFO: Using SRDO reweighting
2025-07-22 09:51:16,333 - INFO: SRDO reweighting completed.
2025-07-22 09:51:16,333 - INFO: Weights calculated and normalized for fold 4.
2025-07-22 09:51:16,333 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.025457
2025-07-22 09:51:16,333 - INFO: After normalization - max: 12.321374, min: 0.000000
2025-07-22 09:51:16,333 - INFO: W before clip: min=0.000000, max=12.321374, mean=1.000000, std=1.525753
2025-07-22 09:51:16,333 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.792158, std=0.887671
2025-07-22 09:51:16,333 - INFO: Starting regression paradigm
2025-07-22 09:51:16,333 - INFO: Training backend model 'LogNormal' on fold 4...
2025-07-22 09:51:16,333 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:51:16,605 - INFO: Fold 4 Training C-index: 0.4981
2025-07-22 09:51:16,868 - INFO: Fold 4 Test C-index: 0.5176
2025-07-22 09:51:16,868 - INFO: --- Fold 5/10 ---
2025-07-22 09:51:16,872 - INFO: Features for this fold have been standardized.
2025-07-22 09:51:16,873 - INFO: Using SRDO reweighting
2025-07-22 09:51:21,142 - INFO: SRDO reweighting completed.
2025-07-22 09:51:21,142 - INFO: Weights calculated and normalized for fold 5.
2025-07-22 09:51:21,142 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.021284
2025-07-22 09:51:21,142 - INFO: After normalization - max: 10.301367, min: 0.000000
2025-07-22 09:51:21,142 - INFO: W before clip: min=0.000000, max=10.301367, mean=1.000000, std=1.384765
2025-07-22 09:51:21,143 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.835281, std=0.906567
2025-07-22 09:51:21,143 - INFO: Starting regression paradigm
2025-07-22 09:51:21,143 - INFO: Training backend model 'LogNormal' on fold 5...
2025-07-22 09:51:21,143 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:51:21,481 - INFO: Fold 5 Training C-index: 0.5376
2025-07-22 09:51:21,741 - INFO: Fold 5 Test C-index: 0.3855
2025-07-22 09:51:21,741 - INFO: --- Fold 6/10 ---
2025-07-22 09:51:21,746 - INFO: Features for this fold have been standardized.
2025-07-22 09:51:21,746 - INFO: Using SRDO reweighting
2025-07-22 09:51:25,716 - INFO: SRDO reweighting completed.
2025-07-22 09:51:25,716 - INFO: Weights calculated and normalized for fold 6.
2025-07-22 09:51:25,716 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.035872
2025-07-22 09:51:25,716 - INFO: After normalization - max: 17.361829, min: 0.000000
2025-07-22 09:51:25,716 - INFO: W before clip: min=0.000000, max=17.361829, mean=1.000000, std=1.542175
2025-07-22 09:51:25,716 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.811203, std=0.865928
2025-07-22 09:51:25,716 - INFO: Starting regression paradigm
2025-07-22 09:51:25,717 - INFO: Training backend model 'LogNormal' on fold 6...
2025-07-22 09:51:25,717 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:51:25,897 - INFO: Fold 6 Training C-index: 0.5054
2025-07-22 09:51:26,138 - INFO: Fold 6 Test C-index: 0.6136
2025-07-22 09:51:26,138 - INFO: Fold 6: New best CV model with fold validation C-index: 0.5812
2025-07-22 09:51:26,138 - INFO: --- Fold 7/10 ---
2025-07-22 09:51:26,143 - INFO: Features for this fold have been standardized.
2025-07-22 09:51:26,143 - INFO: Using SRDO reweighting
2025-07-22 09:51:29,547 - INFO: SRDO reweighting completed.
2025-07-22 09:51:29,547 - INFO: Weights calculated and normalized for fold 7.
2025-07-22 09:51:29,547 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.020302
2025-07-22 09:51:29,547 - INFO: After normalization - max: 9.826370, min: 0.000000
2025-07-22 09:51:29,547 - INFO: W before clip: min=0.000000, max=9.826370, mean=1.000000, std=1.414171
2025-07-22 09:51:29,547 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.831657, std=0.909775
2025-07-22 09:51:29,547 - INFO: Starting regression paradigm
2025-07-22 09:51:29,547 - INFO: Training backend model 'LogNormal' on fold 7...
2025-07-22 09:51:29,547 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:51:29,812 - INFO: Fold 7 Training C-index: 0.5233
2025-07-22 09:51:30,093 - INFO: Fold 7 Test C-index: 0.5375
2025-07-22 09:51:30,093 - INFO: --- Fold 8/10 ---
2025-07-22 09:51:30,099 - INFO: Features for this fold have been standardized.
2025-07-22 09:51:30,099 - INFO: Using SRDO reweighting
2025-07-22 09:51:33,596 - INFO: SRDO reweighting completed.
2025-07-22 09:51:33,596 - INFO: Weights calculated and normalized for fold 8.
2025-07-22 09:51:33,596 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.020482
2025-07-22 09:51:33,596 - INFO: After normalization - max: 9.913149, min: 0.000000
2025-07-22 09:51:33,596 - INFO: W before clip: min=0.000000, max=9.913149, mean=1.000000, std=1.396385
2025-07-22 09:51:33,596 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.823993, std=0.887428
2025-07-22 09:51:33,596 - INFO: Starting regression paradigm
2025-07-22 09:51:33,596 - INFO: Training backend model 'LogNormal' on fold 8...
2025-07-22 09:51:33,596 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:51:33,831 - INFO: Fold 8 Training C-index: 0.5259
2025-07-22 09:51:34,079 - INFO: Fold 8 Test C-index: 0.5618
2025-07-22 09:51:34,079 - INFO: --- Fold 9/10 ---
2025-07-22 09:51:34,084 - INFO: Features for this fold have been standardized.
2025-07-22 09:51:34,085 - INFO: Using SRDO reweighting
2025-07-22 09:51:37,559 - INFO: SRDO reweighting completed.
2025-07-22 09:51:37,560 - INFO: Weights calculated and normalized for fold 9.
2025-07-22 09:51:37,560 - INFO: Weight statistics - mean: 0.002062, min: 0.000000, max: 0.015045
2025-07-22 09:51:37,560 - INFO: After normalization - max: 7.296742, min: 0.000000
2025-07-22 09:51:37,560 - INFO: W before clip: min=0.000000, max=7.296742, mean=1.000000, std=1.338844
2025-07-22 09:51:37,560 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.832426, std=0.900747
2025-07-22 09:51:37,560 - INFO: Starting regression paradigm
2025-07-22 09:51:37,560 - INFO: Training backend model 'LogNormal' on fold 9...
2025-07-22 09:51:37,560 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:51:37,826 - INFO: Fold 9 Training C-index: 0.5173
2025-07-22 09:51:38,243 - INFO: Fold 9 Test C-index: 0.4468
2025-07-22 09:51:38,243 - INFO: --- Fold 10/10 ---
2025-07-22 09:51:38,251 - INFO: Features for this fold have been standardized.
2025-07-22 09:51:38,251 - INFO: Using SRDO reweighting
2025-07-22 09:51:42,641 - INFO: SRDO reweighting completed.
2025-07-22 09:51:42,642 - INFO: Weights calculated and normalized for fold 10.
2025-07-22 09:51:42,642 - INFO: Weight statistics - mean: 0.002062, min: 0.000000, max: 0.024012
2025-07-22 09:51:42,642 - INFO: After normalization - max: 11.645989, min: 0.000000
2025-07-22 09:51:42,642 - INFO: W before clip: min=0.000000, max=11.645989, mean=1.000000, std=1.411279
2025-07-22 09:51:42,642 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.825783, std=0.875398
2025-07-22 09:51:42,642 - INFO: Starting regression paradigm
2025-07-22 09:51:42,642 - INFO: Training backend model 'LogNormal' on fold 10...
2025-07-22 09:51:42,642 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:51:42,905 - INFO: Fold 10 Training C-index: 0.5107
2025-07-22 09:51:43,206 - INFO: Fold 10 Test C-index: 0.7042
2025-07-22 09:51:43,206 - INFO: Fold 10: New best CV model with fold validation C-index: 0.6462
2025-07-22 09:51:43,215 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 1 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-22 09:51:43,215 - INFO: --- Starting Round 1 with Seed 3 ---
2025-07-22 09:51:43,238 - INFO: Successfully loaded data from processed_equipment_data_new.csv. Shape: (598, 187)
2025-07-22 09:51:43,240 - INFO: Succsessfully get feature columns. Total: 180
2025-07-22 09:51:43,246 - INFO: --- Fold 1/10 ---
2025-07-22 09:51:43,289 - INFO: Features for this fold have been standardized.
2025-07-22 09:51:43,295 - INFO: Using SRDO reweighting
2025-07-22 09:51:44,270 - INFO: SRDO reweighting completed.
2025-07-22 09:51:44,270 - INFO: Weights calculated and normalized for fold 1.
2025-07-22 09:51:44,270 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.011411
2025-07-22 09:51:44,270 - INFO: After normalization - max: 5.523070, min: 0.000000
2025-07-22 09:51:44,270 - INFO: W before clip: min=0.000000, max=5.523070, mean=1.000000, std=1.167553
2025-07-22 09:51:44,270 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.897480, std=0.941196
2025-07-22 09:51:44,270 - INFO: Starting regression paradigm
2025-07-22 09:51:44,270 - INFO: Training backend model 'LogNormal' on fold 1...
2025-07-22 09:51:44,270 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:51:45,135 - INFO: Fold 1 Training C-index: 0.5296
2025-07-22 09:51:45,332 - INFO: Fold 1 Test C-index: 0.4872
2025-07-22 09:51:45,332 - INFO: Fold 1: New best CV model with fold validation C-index: 0.4974
2025-07-22 09:51:45,332 - INFO: --- Fold 2/10 ---
2025-07-22 09:51:45,335 - INFO: Features for this fold have been standardized.
2025-07-22 09:51:45,336 - INFO: Using SRDO reweighting
2025-07-22 09:51:49,033 - INFO: SRDO reweighting completed.
2025-07-22 09:51:49,033 - INFO: Weights calculated and normalized for fold 2.
2025-07-22 09:51:49,033 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.025712
2025-07-22 09:51:49,033 - INFO: After normalization - max: 12.444669, min: 0.000000
2025-07-22 09:51:49,034 - INFO: W before clip: min=0.000000, max=12.444669, mean=1.000000, std=1.553794
2025-07-22 09:51:49,034 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.787101, std=0.896970
2025-07-22 09:51:49,034 - INFO: Starting regression paradigm
2025-07-22 09:51:49,034 - INFO: Training backend model 'LogNormal' on fold 2...
2025-07-22 09:51:49,034 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:51:49,210 - INFO: Fold 2 Training C-index: 0.5130
2025-07-22 09:51:49,387 - INFO: Fold 2 Test C-index: 0.4851
2025-07-22 09:51:49,387 - INFO: --- Fold 3/10 ---
2025-07-22 09:51:49,390 - INFO: Features for this fold have been standardized.
2025-07-22 09:51:49,391 - INFO: Using SRDO reweighting
2025-07-22 09:51:53,998 - INFO: SRDO reweighting completed.
2025-07-22 09:51:53,998 - INFO: Weights calculated and normalized for fold 3.
2025-07-22 09:51:53,998 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.023786
2025-07-22 09:51:53,998 - INFO: After normalization - max: 11.512279, min: 0.000000
2025-07-22 09:51:53,999 - INFO: W before clip: min=0.000000, max=11.512279, mean=1.000000, std=1.296221
2025-07-22 09:51:53,999 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.858262, std=0.885020
2025-07-22 09:51:53,999 - INFO: Starting regression paradigm
2025-07-22 09:51:53,999 - INFO: Training backend model 'LogNormal' on fold 3...
2025-07-22 09:51:53,999 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:51:54,266 - INFO: Fold 3 Training C-index: 0.4914
2025-07-22 09:51:54,529 - INFO: Fold 3 Test C-index: 0.6316
2025-07-22 09:51:54,530 - INFO: Fold 3: New best CV model with fold validation C-index: 0.5895
2025-07-22 09:51:54,530 - INFO: --- Fold 4/10 ---
2025-07-22 09:51:54,535 - INFO: Features for this fold have been standardized.
2025-07-22 09:51:54,535 - INFO: Using SRDO reweighting
2025-07-22 09:51:58,038 - INFO: SRDO reweighting completed.
2025-07-22 09:51:58,038 - INFO: Weights calculated and normalized for fold 4.
2025-07-22 09:51:58,038 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.018023
2025-07-22 09:51:58,038 - INFO: After normalization - max: 8.723348, min: 0.000000
2025-07-22 09:51:58,038 - INFO: W before clip: min=0.000000, max=8.723348, mean=1.000000, std=1.375272
2025-07-22 09:51:58,039 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.833816, std=0.918801
2025-07-22 09:51:58,039 - INFO: Starting regression paradigm
2025-07-22 09:51:58,039 - INFO: Training backend model 'LogNormal' on fold 4...
2025-07-22 09:51:58,039 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:51:58,298 - INFO: Fold 4 Training C-index: 0.5046
2025-07-22 09:51:58,569 - INFO: Fold 4 Test C-index: 0.6125
2025-07-22 09:51:58,570 - INFO: --- Fold 5/10 ---
2025-07-22 09:51:58,574 - INFO: Features for this fold have been standardized.
2025-07-22 09:51:58,575 - INFO: Using SRDO reweighting
2025-07-22 09:52:01,856 - INFO: SRDO reweighting completed.
2025-07-22 09:52:01,856 - INFO: Weights calculated and normalized for fold 5.
2025-07-22 09:52:01,856 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.024149
2025-07-22 09:52:01,856 - INFO: After normalization - max: 11.688254, min: 0.000000
2025-07-22 09:52:01,856 - INFO: W before clip: min=0.000000, max=11.688254, mean=1.000000, std=1.593244
2025-07-22 09:52:01,856 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.780935, std=0.889488
2025-07-22 09:52:01,856 - INFO: Starting regression paradigm
2025-07-22 09:52:01,856 - INFO: Training backend model 'LogNormal' on fold 5...
2025-07-22 09:52:01,856 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:52:02,061 - INFO: Fold 5 Training C-index: 0.5143
2025-07-22 09:52:02,401 - INFO: Fold 5 Test C-index: 0.4375
2025-07-22 09:52:02,401 - INFO: --- Fold 6/10 ---
2025-07-22 09:52:02,411 - INFO: Features for this fold have been standardized.
2025-07-22 09:52:02,412 - INFO: Using SRDO reweighting
2025-07-22 09:52:07,736 - INFO: SRDO reweighting completed.
2025-07-22 09:52:07,736 - INFO: Weights calculated and normalized for fold 6.
2025-07-22 09:52:07,737 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.020169
2025-07-22 09:52:07,737 - INFO: After normalization - max: 9.761609, min: 0.000000
2025-07-22 09:52:07,737 - INFO: W before clip: min=0.000000, max=9.761609, mean=1.000000, std=1.370404
2025-07-22 09:52:07,737 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.834547, std=0.910047
2025-07-22 09:52:07,737 - INFO: Starting regression paradigm
2025-07-22 09:52:07,737 - INFO: Training backend model 'LogNormal' on fold 6...
2025-07-22 09:52:07,737 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:52:07,981 - INFO: Fold 6 Training C-index: 0.5448
2025-07-22 09:52:08,270 - INFO: Fold 6 Test C-index: 0.3333
2025-07-22 09:52:08,270 - INFO: --- Fold 7/10 ---
2025-07-22 09:52:08,277 - INFO: Features for this fold have been standardized.
2025-07-22 09:52:08,277 - INFO: Using SRDO reweighting
2025-07-22 09:52:12,893 - INFO: SRDO reweighting completed.
2025-07-22 09:52:12,894 - INFO: Weights calculated and normalized for fold 7.
2025-07-22 09:52:12,894 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.026237
2025-07-22 09:52:12,894 - INFO: After normalization - max: 12.698664, min: 0.000000
2025-07-22 09:52:12,894 - INFO: W before clip: min=0.000000, max=12.698664, mean=1.000000, std=1.546854
2025-07-22 09:52:12,894 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.800416, std=0.892083
2025-07-22 09:52:12,894 - INFO: Starting regression paradigm
2025-07-22 09:52:12,894 - INFO: Training backend model 'LogNormal' on fold 7...
2025-07-22 09:52:12,894 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:52:13,314 - INFO: Fold 7 Training C-index: 0.4962
2025-07-22 09:52:13,738 - INFO: Fold 7 Test C-index: 0.6706
2025-07-22 09:52:13,738 - INFO: Fold 7: New best CV model with fold validation C-index: 0.6183
2025-07-22 09:52:13,739 - INFO: --- Fold 8/10 ---
2025-07-22 09:52:13,747 - INFO: Features for this fold have been standardized.
2025-07-22 09:52:13,748 - INFO: Using SRDO reweighting
2025-07-22 09:52:18,348 - INFO: SRDO reweighting completed.
2025-07-22 09:52:18,348 - INFO: Weights calculated and normalized for fold 8.
2025-07-22 09:52:18,348 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.018732
2025-07-22 09:52:18,348 - INFO: After normalization - max: 9.066093, min: 0.000000
2025-07-22 09:52:18,349 - INFO: W before clip: min=0.000000, max=9.066093, mean=1.000000, std=1.401705
2025-07-22 09:52:18,349 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.821035, std=0.892394
2025-07-22 09:52:18,349 - INFO: Starting regression paradigm
2025-07-22 09:52:18,349 - INFO: Training backend model 'LogNormal' on fold 8...
2025-07-22 09:52:18,349 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:52:18,768 - INFO: Fold 8 Training C-index: 0.5254
2025-07-22 09:52:19,602 - INFO: Fold 8 Test C-index: 0.5125
2025-07-22 09:52:19,602 - INFO: --- Fold 9/10 ---
2025-07-22 09:52:19,610 - INFO: Features for this fold have been standardized.
2025-07-22 09:52:19,611 - INFO: Using SRDO reweighting
2025-07-22 09:52:24,979 - INFO: SRDO reweighting completed.
2025-07-22 09:52:24,979 - INFO: Weights calculated and normalized for fold 9.
2025-07-22 09:52:24,980 - INFO: Weight statistics - mean: 0.002062, min: 0.000000, max: 0.019384
2025-07-22 09:52:24,980 - INFO: After normalization - max: 9.401238, min: 0.000000
2025-07-22 09:52:24,980 - INFO: W before clip: min=0.000000, max=9.401238, mean=1.000000, std=1.293563
2025-07-22 09:52:24,980 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.861925, std=0.924788
2025-07-22 09:52:24,980 - INFO: Starting regression paradigm
2025-07-22 09:52:24,981 - INFO: Training backend model 'LogNormal' on fold 9...
2025-07-22 09:52:24,981 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:52:25,251 - INFO: Fold 9 Training C-index: 0.5178
2025-07-22 09:52:25,557 - INFO: Fold 9 Test C-index: 0.5000
2025-07-22 09:52:25,557 - INFO: --- Fold 10/10 ---
2025-07-22 09:52:25,562 - INFO: Features for this fold have been standardized.
2025-07-22 09:52:25,562 - INFO: Using SRDO reweighting
2025-07-22 09:52:29,640 - INFO: SRDO reweighting completed.
2025-07-22 09:52:29,640 - INFO: Weights calculated and normalized for fold 10.
2025-07-22 09:52:29,640 - INFO: Weight statistics - mean: 0.002062, min: 0.000000, max: 0.022724
2025-07-22 09:52:29,641 - INFO: After normalization - max: 11.021304, min: 0.000000
2025-07-22 09:52:29,641 - INFO: W before clip: min=0.000000, max=11.021304, mean=1.000000, std=1.454034
2025-07-22 09:52:29,641 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.818589, std=0.896706
2025-07-22 09:52:29,641 - INFO: Starting regression paradigm
2025-07-22 09:52:29,641 - INFO: Training backend model 'LogNormal' on fold 10...
2025-07-22 09:52:29,641 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:52:29,898 - INFO: Fold 10 Training C-index: 0.5226
2025-07-22 09:52:30,167 - INFO: Fold 10 Test C-index: 0.4394
2025-07-22 09:52:30,173 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 2 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-22 09:52:30,174 - INFO: --- Starting Round 2 with Seed 4 ---
2025-07-22 09:52:30,193 - INFO: Successfully loaded data from processed_equipment_data_new.csv. Shape: (598, 187)
2025-07-22 09:52:30,194 - INFO: Succsessfully get feature columns. Total: 180
2025-07-22 09:52:30,197 - INFO: --- Fold 1/10 ---
2025-07-22 09:52:30,219 - INFO: Features for this fold have been standardized.
2025-07-22 09:52:30,222 - INFO: Using SRDO reweighting
2025-07-22 09:52:30,948 - INFO: SRDO reweighting completed.
2025-07-22 09:52:30,948 - INFO: Weights calculated and normalized for fold 1.
2025-07-22 09:52:30,949 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.014943
2025-07-22 09:52:30,949 - INFO: After normalization - max: 7.232551, min: 0.000000
2025-07-22 09:52:30,949 - INFO: W before clip: min=0.000000, max=7.232551, mean=1.000000, std=1.191343
2025-07-22 09:52:30,949 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.903817, std=0.953015
2025-07-22 09:52:30,949 - INFO: Starting regression paradigm
2025-07-22 09:52:30,949 - INFO: Training backend model 'LogNormal' on fold 1...
2025-07-22 09:52:30,949 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:52:31,915 - INFO: Fold 1 Training C-index: 0.5332
2025-07-22 09:52:32,192 - INFO: Fold 1 Test C-index: 0.4615
2025-07-22 09:52:32,192 - INFO: Fold 1: New best CV model with fold validation C-index: 0.4831
2025-07-22 09:52:32,192 - INFO: --- Fold 2/10 ---
2025-07-22 09:52:32,197 - INFO: Features for this fold have been standardized.
2025-07-22 09:52:32,197 - INFO: Using SRDO reweighting
2025-07-22 09:52:39,046 - INFO: SRDO reweighting completed.
2025-07-22 09:52:39,046 - INFO: Weights calculated and normalized for fold 2.
2025-07-22 09:52:39,046 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.015430
2025-07-22 09:52:39,046 - INFO: After normalization - max: 7.467905, min: 0.000000
2025-07-22 09:52:39,046 - INFO: W before clip: min=0.000000, max=7.467905, mean=1.000000, std=1.373917
2025-07-22 09:52:39,046 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.820172, std=0.865666
2025-07-22 09:52:39,046 - INFO: Starting regression paradigm
2025-07-22 09:52:39,046 - INFO: Training backend model 'LogNormal' on fold 2...
2025-07-22 09:52:39,046 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:52:39,325 - INFO: Fold 2 Training C-index: 0.5063
2025-07-22 09:52:39,594 - INFO: Fold 2 Test C-index: 0.5375
2025-07-22 09:52:39,594 - INFO: Fold 2: New best CV model with fold validation C-index: 0.5281
2025-07-22 09:52:39,594 - INFO: --- Fold 3/10 ---
2025-07-22 09:52:39,599 - INFO: Features for this fold have been standardized.
2025-07-22 09:52:39,599 - INFO: Using SRDO reweighting
2025-07-22 09:52:44,449 - INFO: SRDO reweighting completed.
2025-07-22 09:52:44,449 - INFO: Weights calculated and normalized for fold 3.
2025-07-22 09:52:44,449 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.022256
2025-07-22 09:52:44,449 - INFO: After normalization - max: 10.771782, min: 0.000000
2025-07-22 09:52:44,450 - INFO: W before clip: min=0.000000, max=10.771782, mean=1.000000, std=1.447434
2025-07-22 09:52:44,450 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.826748, std=0.884851
2025-07-22 09:52:44,450 - INFO: Starting regression paradigm
2025-07-22 09:52:44,450 - INFO: Training backend model 'LogNormal' on fold 3...
2025-07-22 09:52:44,450 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:52:44,841 - INFO: Fold 3 Training C-index: 0.5121
2025-07-22 09:52:45,162 - INFO: Fold 3 Test C-index: 0.6080
2025-07-22 09:52:45,162 - INFO: Fold 3: New best CV model with fold validation C-index: 0.5792
2025-07-22 09:52:45,162 - INFO: --- Fold 4/10 ---
2025-07-22 09:52:45,167 - INFO: Features for this fold have been standardized.
2025-07-22 09:52:45,168 - INFO: Using SRDO reweighting
2025-07-22 09:52:50,259 - INFO: SRDO reweighting completed.
2025-07-22 09:52:50,259 - INFO: Weights calculated and normalized for fold 4.
2025-07-22 09:52:50,259 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.018028
2025-07-22 09:52:50,259 - INFO: After normalization - max: 8.725348, min: 0.000000
2025-07-22 09:52:50,259 - INFO: W before clip: min=0.000000, max=8.725348, mean=1.000000, std=1.363756
2025-07-22 09:52:50,259 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.828006, std=0.889908
2025-07-22 09:52:50,259 - INFO: Starting regression paradigm
2025-07-22 09:52:50,259 - INFO: Training backend model 'LogNormal' on fold 4...
2025-07-22 09:52:50,259 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:52:50,563 - INFO: Fold 4 Training C-index: 0.5213
2025-07-22 09:52:50,837 - INFO: Fold 4 Test C-index: 0.4268
2025-07-22 09:52:50,837 - INFO: --- Fold 5/10 ---
2025-07-22 09:52:50,845 - INFO: Features for this fold have been standardized.
2025-07-22 09:52:50,846 - INFO: Using SRDO reweighting
2025-07-22 09:52:55,572 - INFO: SRDO reweighting completed.
2025-07-22 09:52:55,573 - INFO: Weights calculated and normalized for fold 5.
2025-07-22 09:52:55,573 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.023337
2025-07-22 09:52:55,573 - INFO: After normalization - max: 11.295134, min: 0.000000
2025-07-22 09:52:55,573 - INFO: W before clip: min=0.000000, max=11.295134, mean=1.000000, std=1.358146
2025-07-22 09:52:55,573 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.837952, std=0.886009
2025-07-22 09:52:55,573 - INFO: Starting regression paradigm
2025-07-22 09:52:55,574 - INFO: Training backend model 'LogNormal' on fold 5...
2025-07-22 09:52:55,574 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:52:55,834 - INFO: Fold 5 Training C-index: 0.5168
2025-07-22 09:52:56,100 - INFO: Fold 5 Test C-index: 0.4824
2025-07-22 09:52:56,100 - INFO: --- Fold 6/10 ---
2025-07-22 09:52:56,105 - INFO: Features for this fold have been standardized.
2025-07-22 09:52:56,105 - INFO: Using SRDO reweighting
2025-07-22 09:53:00,162 - INFO: SRDO reweighting completed.
2025-07-22 09:53:00,162 - INFO: Weights calculated and normalized for fold 6.
2025-07-22 09:53:00,162 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.038636
2025-07-22 09:53:00,162 - INFO: After normalization - max: 18.699787, min: 0.000000
2025-07-22 09:53:00,162 - INFO: W before clip: min=0.000000, max=18.699787, mean=1.000000, std=1.644729
2025-07-22 09:53:00,163 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.791730, std=0.884860
2025-07-22 09:53:00,163 - INFO: Starting regression paradigm
2025-07-22 09:53:00,163 - INFO: Training backend model 'LogNormal' on fold 6...
2025-07-22 09:53:00,163 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:53:00,422 - INFO: Fold 6 Training C-index: 0.5134
2025-07-22 09:53:00,697 - INFO: Fold 6 Test C-index: 0.5195
2025-07-22 09:53:00,697 - INFO: --- Fold 7/10 ---
2025-07-22 09:53:00,702 - INFO: Features for this fold have been standardized.
2025-07-22 09:53:00,702 - INFO: Using SRDO reweighting
2025-07-22 09:53:05,245 - INFO: SRDO reweighting completed.
2025-07-22 09:53:05,245 - INFO: Weights calculated and normalized for fold 7.
2025-07-22 09:53:05,246 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.044949
2025-07-22 09:53:05,246 - INFO: After normalization - max: 21.755162, min: 0.000000
2025-07-22 09:53:05,246 - INFO: W before clip: min=0.000000, max=21.755162, mean=1.000000, std=1.605823
2025-07-22 09:53:05,246 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.818539, std=0.888289
2025-07-22 09:53:05,246 - INFO: Starting regression paradigm
2025-07-22 09:53:05,246 - INFO: Training backend model 'LogNormal' on fold 7...
2025-07-22 09:53:05,247 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:53:05,504 - INFO: Fold 7 Training C-index: 0.5023
2025-07-22 09:53:05,768 - INFO: Fold 7 Test C-index: 0.5957
2025-07-22 09:53:05,768 - INFO: --- Fold 8/10 ---
2025-07-22 09:53:05,772 - INFO: Features for this fold have been standardized.
2025-07-22 09:53:05,773 - INFO: Using SRDO reweighting
2025-07-22 09:53:10,221 - INFO: SRDO reweighting completed.
2025-07-22 09:53:10,221 - INFO: Weights calculated and normalized for fold 8.
2025-07-22 09:53:10,221 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.014015
2025-07-22 09:53:10,221 - INFO: After normalization - max: 6.783169, min: 0.000000
2025-07-22 09:53:10,221 - INFO: W before clip: min=0.000000, max=6.783169, mean=1.000000, std=1.295248
2025-07-22 09:53:10,221 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.853140, std=0.900634
2025-07-22 09:53:10,221 - INFO: Starting regression paradigm
2025-07-22 09:53:10,221 - INFO: Training backend model 'LogNormal' on fold 8...
2025-07-22 09:53:10,221 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:53:10,422 - INFO: Fold 8 Training C-index: 0.5315
2025-07-22 09:53:10,684 - INFO: Fold 8 Test C-index: 0.4487
2025-07-22 09:53:10,684 - INFO: --- Fold 9/10 ---
2025-07-22 09:53:10,691 - INFO: Features for this fold have been standardized.
2025-07-22 09:53:10,692 - INFO: Using SRDO reweighting
2025-07-22 09:53:15,552 - INFO: SRDO reweighting completed.
2025-07-22 09:53:15,552 - INFO: Weights calculated and normalized for fold 9.
2025-07-22 09:53:15,552 - INFO: Weight statistics - mean: 0.002062, min: 0.000000, max: 0.014624
2025-07-22 09:53:15,552 - INFO: After normalization - max: 7.092672, min: 0.000000
2025-07-22 09:53:15,552 - INFO: W before clip: min=0.000000, max=7.092672, mean=1.000000, std=1.360752
2025-07-22 09:53:15,553 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.831693, std=0.918643
2025-07-22 09:53:15,553 - INFO: Starting regression paradigm
2025-07-22 09:53:15,553 - INFO: Training backend model 'LogNormal' on fold 9...
2025-07-22 09:53:15,553 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:53:15,815 - INFO: Fold 9 Training C-index: 0.5125
2025-07-22 09:53:16,089 - INFO: Fold 9 Test C-index: 0.5616
2025-07-22 09:53:16,089 - INFO: --- Fold 10/10 ---
2025-07-22 09:53:16,096 - INFO: Features for this fold have been standardized.
2025-07-22 09:53:16,097 - INFO: Using SRDO reweighting
2025-07-22 09:53:21,277 - INFO: SRDO reweighting completed.
2025-07-22 09:53:21,277 - INFO: Weights calculated and normalized for fold 10.
2025-07-22 09:53:21,278 - INFO: Weight statistics - mean: 0.002062, min: 0.000000, max: 0.026401
2025-07-22 09:53:21,278 - INFO: After normalization - max: 12.804305, min: 0.000000
2025-07-22 09:53:21,278 - INFO: W before clip: min=0.000000, max=12.804305, mean=1.000000, std=1.604790
2025-07-22 09:53:21,278 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.783732, std=0.865585
2025-07-22 09:53:21,278 - INFO: Starting regression paradigm
2025-07-22 09:53:21,278 - INFO: Training backend model 'LogNormal' on fold 10...
2025-07-22 09:53:21,278 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:53:21,536 - INFO: Fold 10 Training C-index: 0.5304
2025-07-22 09:53:21,788 - INFO: Fold 10 Test C-index: 0.3649
2025-07-22 09:53:21,795 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 3 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-22 09:53:21,795 - INFO: --- Starting Round 3 with Seed 5 ---
2025-07-22 09:53:21,814 - INFO: Successfully loaded data from processed_equipment_data_new.csv. Shape: (598, 187)
2025-07-22 09:53:21,815 - INFO: Succsessfully get feature columns. Total: 180
2025-07-22 09:53:21,818 - INFO: --- Fold 1/10 ---
2025-07-22 09:53:21,839 - INFO: Features for this fold have been standardized.
2025-07-22 09:53:21,842 - INFO: Using SRDO reweighting
2025-07-22 09:53:22,741 - INFO: SRDO reweighting completed.
2025-07-22 09:53:22,741 - INFO: Weights calculated and normalized for fold 1.
2025-07-22 09:53:22,742 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.014908
2025-07-22 09:53:22,742 - INFO: After normalization - max: 7.215261, min: 0.000000
2025-07-22 09:53:22,742 - INFO: W before clip: min=0.000000, max=7.215261, mean=1.000000, std=1.239578
2025-07-22 09:53:22,742 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.883483, std=0.961740
2025-07-22 09:53:22,742 - INFO: Starting regression paradigm
2025-07-22 09:53:22,742 - INFO: Training backend model 'LogNormal' on fold 1...
2025-07-22 09:53:22,742 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:53:23,746 - INFO: Fold 1 Training C-index: 0.5213
2025-07-22 09:53:24,017 - INFO: Fold 1 Test C-index: 0.5128
2025-07-22 09:53:24,017 - INFO: Fold 1: New best CV model with fold validation C-index: 0.5250
2025-07-22 09:53:24,017 - INFO: --- Fold 2/10 ---
2025-07-22 09:53:24,022 - INFO: Features for this fold have been standardized.
2025-07-22 09:53:24,023 - INFO: Using SRDO reweighting
2025-07-22 09:53:28,263 - INFO: SRDO reweighting completed.
2025-07-22 09:53:28,263 - INFO: Weights calculated and normalized for fold 2.
2025-07-22 09:53:28,263 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.035008
2025-07-22 09:53:28,264 - INFO: After normalization - max: 16.943787, min: 0.000000
2025-07-22 09:53:28,264 - INFO: W before clip: min=0.000000, max=16.943787, mean=1.000000, std=1.626646
2025-07-22 09:53:28,264 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.787898, std=0.858307
2025-07-22 09:53:28,264 - INFO: Starting regression paradigm
2025-07-22 09:53:28,264 - INFO: Training backend model 'LogNormal' on fold 2...
2025-07-22 09:53:28,264 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:53:28,721 - INFO: Fold 2 Training C-index: 0.5377
2025-07-22 09:53:29,095 - INFO: Fold 2 Test C-index: 0.5100
2025-07-22 09:53:29,095 - INFO: --- Fold 3/10 ---
2025-07-22 09:53:29,101 - INFO: Features for this fold have been standardized.
2025-07-22 09:53:29,101 - INFO: Using SRDO reweighting
2025-07-22 09:53:34,573 - INFO: SRDO reweighting completed.
2025-07-22 09:53:34,573 - INFO: Weights calculated and normalized for fold 3.
2025-07-22 09:53:34,573 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.021844
2025-07-22 09:53:34,573 - INFO: After normalization - max: 10.572558, min: 0.000000
2025-07-22 09:53:34,573 - INFO: W before clip: min=0.000000, max=10.572558, mean=1.000000, std=1.468133
2025-07-22 09:53:34,574 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.804236, std=0.868425
2025-07-22 09:53:34,574 - INFO: Starting regression paradigm
2025-07-22 09:53:34,574 - INFO: Training backend model 'LogNormal' on fold 3...
2025-07-22 09:53:34,574 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:53:34,826 - INFO: Fold 3 Training C-index: 0.5430
2025-07-22 09:53:35,083 - INFO: Fold 3 Test C-index: 0.5368
2025-07-22 09:53:35,083 - INFO: Fold 3: New best CV model with fold validation C-index: 0.5387
2025-07-22 09:53:35,084 - INFO: --- Fold 4/10 ---
2025-07-22 09:53:35,088 - INFO: Features for this fold have been standardized.
2025-07-22 09:53:35,089 - INFO: Using SRDO reweighting
2025-07-22 09:53:39,829 - INFO: SRDO reweighting completed.
2025-07-22 09:53:39,830 - INFO: Weights calculated and normalized for fold 4.
2025-07-22 09:53:39,830 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.024354
2025-07-22 09:53:39,830 - INFO: After normalization - max: 11.787299, min: 0.000000
2025-07-22 09:53:39,830 - INFO: W before clip: min=0.000000, max=11.787299, mean=1.000000, std=1.495283
2025-07-22 09:53:39,830 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.805572, std=0.893325
2025-07-22 09:53:39,831 - INFO: Starting regression paradigm
2025-07-22 09:53:39,831 - INFO: Training backend model 'LogNormal' on fold 4...
2025-07-22 09:53:39,831 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:53:40,081 - INFO: Fold 4 Training C-index: 0.5528
2025-07-22 09:53:40,359 - INFO: Fold 4 Test C-index: 0.5529
2025-07-22 09:53:40,359 - INFO: Fold 4: New best CV model with fold validation C-index: 0.5529
2025-07-22 09:53:40,359 - INFO: --- Fold 5/10 ---
2025-07-22 09:53:40,364 - INFO: Features for this fold have been standardized.
2025-07-22 09:53:40,365 - INFO: Using SRDO reweighting
2025-07-22 09:53:44,592 - INFO: SRDO reweighting completed.
2025-07-22 09:53:44,593 - INFO: Weights calculated and normalized for fold 5.
2025-07-22 09:53:44,593 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.015194
2025-07-22 09:53:44,593 - INFO: After normalization - max: 7.354099, min: 0.000000
2025-07-22 09:53:44,593 - INFO: W before clip: min=0.000000, max=7.354099, mean=1.000000, std=1.323627
2025-07-22 09:53:44,593 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.841522, std=0.911140
2025-07-22 09:53:44,593 - INFO: Starting regression paradigm
2025-07-22 09:53:44,593 - INFO: Training backend model 'LogNormal' on fold 5...
2025-07-22 09:53:44,593 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:53:44,781 - INFO: Fold 5 Training C-index: 0.5414
2025-07-22 09:53:44,966 - INFO: Fold 5 Test C-index: 0.6163
2025-07-22 09:53:44,966 - INFO: Fold 5: New best CV model with fold validation C-index: 0.5938
2025-07-22 09:53:44,966 - INFO: --- Fold 6/10 ---
2025-07-22 09:53:44,969 - INFO: Features for this fold have been standardized.
2025-07-22 09:53:44,969 - INFO: Using SRDO reweighting
2025-07-22 09:53:50,211 - INFO: SRDO reweighting completed.
2025-07-22 09:53:50,211 - INFO: Weights calculated and normalized for fold 6.
2025-07-22 09:53:50,211 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.026913
2025-07-22 09:53:50,211 - INFO: After normalization - max: 13.025953, min: 0.000000
2025-07-22 09:53:50,211 - INFO: W before clip: min=0.000000, max=13.025953, mean=1.000000, std=1.481201
2025-07-22 09:53:50,212 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.818111, std=0.903996
2025-07-22 09:53:50,212 - INFO: Starting regression paradigm
2025-07-22 09:53:50,212 - INFO: Training backend model 'LogNormal' on fold 6...
2025-07-22 09:53:50,212 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:53:50,630 - INFO: Fold 6 Training C-index: 0.5472
2025-07-22 09:53:50,858 - INFO: Fold 6 Test C-index: 0.5000
2025-07-22 09:53:50,858 - INFO: --- Fold 7/10 ---
2025-07-22 09:53:50,862 - INFO: Features for this fold have been standardized.
2025-07-22 09:53:50,862 - INFO: Using SRDO reweighting
2025-07-22 09:53:56,305 - INFO: SRDO reweighting completed.
2025-07-22 09:53:56,305 - INFO: Weights calculated and normalized for fold 7.
2025-07-22 09:53:56,306 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.019022
2025-07-22 09:53:56,306 - INFO: After normalization - max: 9.206692, min: 0.000000
2025-07-22 09:53:56,306 - INFO: W before clip: min=0.000000, max=9.206692, mean=1.000000, std=1.427909
2025-07-22 09:53:56,306 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.820363, std=0.915628
2025-07-22 09:53:56,306 - INFO: Starting regression paradigm
2025-07-22 09:53:56,306 - INFO: Training backend model 'LogNormal' on fold 7...
2025-07-22 09:53:56,306 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:53:56,648 - INFO: Fold 7 Training C-index: 0.5474
2025-07-22 09:53:56,842 - INFO: Fold 7 Test C-index: 0.4568
2025-07-22 09:53:56,842 - INFO: --- Fold 8/10 ---
2025-07-22 09:53:56,846 - INFO: Features for this fold have been standardized.
2025-07-22 09:53:56,846 - INFO: Using SRDO reweighting
2025-07-22 09:54:02,259 - INFO: SRDO reweighting completed.
2025-07-22 09:54:02,259 - INFO: Weights calculated and normalized for fold 8.
2025-07-22 09:54:02,259 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.033093
2025-07-22 09:54:02,259 - INFO: After normalization - max: 16.016918, min: 0.000000
2025-07-22 09:54:02,260 - INFO: W before clip: min=0.000000, max=16.016918, mean=1.000000, std=1.468331
2025-07-22 09:54:02,260 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.820397, std=0.884039
2025-07-22 09:54:02,260 - INFO: Starting regression paradigm
2025-07-22 09:54:02,260 - INFO: Training backend model 'LogNormal' on fold 8...
2025-07-22 09:54:02,260 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:54:02,692 - INFO: Fold 8 Training C-index: 0.5341
2025-07-22 09:54:03,010 - INFO: Fold 8 Test C-index: 0.6203
2025-07-22 09:54:03,010 - INFO: Fold 8: New best CV model with fold validation C-index: 0.5944
2025-07-22 09:54:03,010 - INFO: --- Fold 9/10 ---
2025-07-22 09:54:03,020 - INFO: Features for this fold have been standardized.
2025-07-22 09:54:03,021 - INFO: Using SRDO reweighting
2025-07-22 09:54:09,365 - INFO: SRDO reweighting completed.
2025-07-22 09:54:09,366 - INFO: Weights calculated and normalized for fold 9.
2025-07-22 09:54:09,366 - INFO: Weight statistics - mean: 0.002062, min: 0.000000, max: 0.023516
2025-07-22 09:54:09,366 - INFO: After normalization - max: 11.405297, min: 0.000000
2025-07-22 09:54:09,366 - INFO: W before clip: min=0.000000, max=11.405297, mean=1.000000, std=1.438994
2025-07-22 09:54:09,366 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.816686, std=0.883623
2025-07-22 09:54:09,366 - INFO: Starting regression paradigm
2025-07-22 09:54:09,367 - INFO: Training backend model 'LogNormal' on fold 9...
2025-07-22 09:54:09,367 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:54:09,615 - INFO: Fold 9 Training C-index: 0.5482
2025-07-22 09:54:09,887 - INFO: Fold 9 Test C-index: 0.4776
2025-07-22 09:54:09,887 - INFO: --- Fold 10/10 ---
2025-07-22 09:54:09,892 - INFO: Features for this fold have been standardized.
2025-07-22 09:54:09,893 - INFO: Using SRDO reweighting
2025-07-22 09:54:14,066 - INFO: SRDO reweighting completed.
2025-07-22 09:54:14,066 - INFO: Weights calculated and normalized for fold 10.
2025-07-22 09:54:14,067 - INFO: Weight statistics - mean: 0.002062, min: 0.000000, max: 0.019219
2025-07-22 09:54:14,067 - INFO: After normalization - max: 9.321270, min: 0.000000
2025-07-22 09:54:14,067 - INFO: W before clip: min=0.000000, max=9.321270, mean=1.000000, std=1.407110
2025-07-22 09:54:14,067 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.817975, std=0.892086
2025-07-22 09:54:14,067 - INFO: Starting regression paradigm
2025-07-22 09:54:14,067 - INFO: Training backend model 'LogNormal' on fold 10...
2025-07-22 09:54:14,068 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:54:14,428 - INFO: Fold 10 Training C-index: 0.5364
2025-07-22 09:54:14,740 - INFO: Fold 10 Test C-index: 0.6269
2025-07-22 09:54:14,740 - INFO: Fold 10: New best CV model with fold validation C-index: 0.5997
2025-07-22 09:54:14,747 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 4 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-22 09:54:14,747 - INFO: --- Starting Round 4 with Seed 6 ---
2025-07-22 09:54:14,769 - INFO: Successfully loaded data from processed_equipment_data_new.csv. Shape: (598, 187)
2025-07-22 09:54:14,770 - INFO: Succsessfully get feature columns. Total: 180
2025-07-22 09:54:14,773 - INFO: --- Fold 1/10 ---
2025-07-22 09:54:14,797 - INFO: Features for this fold have been standardized.
2025-07-22 09:54:14,805 - INFO: Using SRDO reweighting
2025-07-22 09:54:15,775 - INFO: SRDO reweighting completed.
2025-07-22 09:54:15,776 - INFO: Weights calculated and normalized for fold 1.
2025-07-22 09:54:15,776 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.015847
2025-07-22 09:54:15,776 - INFO: After normalization - max: 7.670083, min: 0.000000
2025-07-22 09:54:15,776 - INFO: W before clip: min=0.000000, max=7.670083, mean=1.000000, std=1.190750
2025-07-22 09:54:15,776 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.902590, std=0.936452
2025-07-22 09:54:15,776 - INFO: Starting regression paradigm
2025-07-22 09:54:15,776 - INFO: Training backend model 'LogNormal' on fold 1...
2025-07-22 09:54:15,776 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:54:16,769 - INFO: Fold 1 Training C-index: 0.4974
2025-07-22 09:54:17,131 - INFO: Fold 1 Test C-index: 0.3293
2025-07-22 09:54:17,131 - INFO: Fold 1: New best CV model with fold validation C-index: 0.3882
2025-07-22 09:54:17,131 - INFO: --- Fold 2/10 ---
2025-07-22 09:54:17,140 - INFO: Features for this fold have been standardized.
2025-07-22 09:54:17,141 - INFO: Using SRDO reweighting
2025-07-22 09:54:23,862 - INFO: SRDO reweighting completed.
2025-07-22 09:54:23,862 - INFO: Weights calculated and normalized for fold 2.
2025-07-22 09:54:23,862 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.028803
2025-07-22 09:54:23,862 - INFO: After normalization - max: 13.940743, min: 0.000000
2025-07-22 09:54:23,862 - INFO: W before clip: min=0.000000, max=13.940743, mean=1.000000, std=1.403799
2025-07-22 09:54:23,862 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.839020, std=0.894794
2025-07-22 09:54:23,862 - INFO: Starting regression paradigm
2025-07-22 09:54:23,862 - INFO: Training backend model 'LogNormal' on fold 2...
2025-07-22 09:54:23,862 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:54:24,233 - INFO: Fold 2 Training C-index: 0.5151
2025-07-22 09:54:24,667 - INFO: Fold 2 Test C-index: 0.5000
2025-07-22 09:54:24,667 - INFO: Fold 2: New best CV model with fold validation C-index: 0.5045
2025-07-22 09:54:24,667 - INFO: --- Fold 3/10 ---
2025-07-22 09:54:24,671 - INFO: Features for this fold have been standardized.
2025-07-22 09:54:24,672 - INFO: Using SRDO reweighting
2025-07-22 09:54:29,824 - INFO: SRDO reweighting completed.
2025-07-22 09:54:29,824 - INFO: Weights calculated and normalized for fold 3.
2025-07-22 09:54:29,824 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.021238
2025-07-22 09:54:29,825 - INFO: After normalization - max: 10.279134, min: 0.000000
2025-07-22 09:54:29,825 - INFO: W before clip: min=0.000000, max=10.279134, mean=1.000000, std=1.421521
2025-07-22 09:54:29,825 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.813075, std=0.884366
2025-07-22 09:54:29,825 - INFO: Starting regression paradigm
2025-07-22 09:54:29,825 - INFO: Training backend model 'LogNormal' on fold 3...
2025-07-22 09:54:29,825 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:54:30,167 - INFO: Fold 3 Training C-index: 0.5139
Number of selected features: 10
Selected features 9, top 5: ['EPI_700V_X2_LOW', 'Z_CLOSED_LOOP_V280_L_Y', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y']
Number of selected features: 10
Selected features 9, top 5: ['EPI_700V_X2_LOW', 'Z_CLOSED_LOOP_V280_L_Y', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y']
Number of selected features: 10
Selected features 9, top 5: ['EPI_700V_X2_LOW', 'Z_CLOSED_LOOP_V280_L_Y', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y']
Number of selected features: 10
Selected features 9, top 5: ['EPI_700V_X2_LOW', 'Z_CLOSED_LOOP_V280_L_Y', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y']
Number of selected features: 10
Selected features 9, top 5: ['EPI_700V_X2_LOW', 'Z_CLOSED_LOOP_V280_L_Y', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y']
Number of selected features: 10
Selected features 9, top 5: ['EPI_700V_X2_LOW', 'Z_CLOSED_LOOP_V280_L_Y', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y']
Number of selected features: 10
Selected features 9, top 5: ['EPI_700V_X2_LOW', 'Z_CLOSED_LOOP_V280_L_Y', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y']
Number of selected features: 10
Selected features 9, top 5: ['EPI_700V_X2_LOW', 'Z_CLOSED_LOOP_V280_L_Y', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y']
Number of selected features: 10
Selected features 9, top 5: ['EPI_700V_X2_LOW', 'Z_CLOSED_LOOP_V280_L_Y', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y']
Number of selected features: 10
Selected features 9, top 5: ['EPI_700V_X2_LOW', 'Z_CLOSED_LOOP_V280_L_Y', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y']
Number of selected features: 10
Selected features 9, top 5: ['300V_V700_H_X', 'EPI_700V_X1_LOW', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'EPI_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y']
Number of selected features: 10
Selected features 9, top 5: ['300V_V700_H_X', 'EPI_700V_X1_LOW', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'EPI_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y']
Number of selected features: 10
Selected features 9, top 5: ['300V_V700_H_X', 'EPI_700V_X1_LOW', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'EPI_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y']
Number of selected features: 10
Selected features 9, top 5: ['300V_V700_H_X', 'EPI_700V_X1_LOW', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'EPI_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y']
Number of selected features: 10
Selected features 9, top 5: ['300V_V700_H_X', 'EPI_700V_X1_LOW', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'EPI_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y']
Number of selected features: 10
Selected features 9, top 5: ['300V_V700_H_X', 'EPI_700V_X1_LOW', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'EPI_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y']
Number of selected features: 10
Selected features 9, top 5: ['300V_V700_H_X', 'EPI_700V_X1_LOW', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'EPI_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y']
Number of selected features: 10
Selected features 9, top 5: ['300V_V700_H_X', 'EPI_700V_X1_LOW', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'EPI_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y']
Number of selected features: 10
Selected features 9, top 5: ['300V_V700_H_X', 'EPI_700V_X1_LOW', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'EPI_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y']
Number of selected features: 10
Selected features 9, top 5: ['300V_V700_H_X', 'EPI_700V_X1_LOW', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'EPI_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y']
Number of selected features: 10
Selected features 9, top 5: ['PULSE_280V_Z1_LOW', 'EPI_700V_X2_LOW', 'Heat_Sink_Temp_Amplifier_Lavaflex', 'Heat_Sink_Temp_PS_Lavaflex', 'OUTPUT_VOLTAGE_DISCH_V280_L_X']
Number of selected features: 10
Selected features 9, top 5: ['PULSE_280V_Z1_LOW', 'EPI_700V_X2_LOW', 'Heat_Sink_Temp_Amplifier_Lavaflex', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X']
Number of selected features: 10
Selected features 9, top 5: ['PULSE_280V_Z1_LOW', 'EPI_700V_X2_LOW', 'Heat_Sink_Temp_Amplifier_Lavaflex', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X']
Number of selected features: 10
Selected features 9, top 5: ['PULSE_280V_Z1_LOW', 'EPI_700V_X2_LOW', 'Heat_Sink_Temp_Amplifier_Lavaflex', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X']
Number of selected features: 10
Selected features 9, top 5: ['PULSE_280V_Z1_LOW', 'EPI_700V_X2_LOW', 'Heat_Sink_Temp_Amplifier_Lavaflex', 'Heat_Sink_Temp_PS_Lavaflex', 'OUTPUT_VOLTAGE_DISCH_V280_L_X']
Number of selected features: 10
Selected features 9, top 5: ['PULSE_280V_Z1_LOW', 'EPI_700V_X2_LOW', 'Heat_Sink_Temp_Amplifier_Lavaflex', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X']
Number of selected features: 10
Selected features 9, top 5: ['PULSE_280V_Z1_LOW', 'EPI_700V_X2_LOW', 'Heat_Sink_Temp_Amplifier_Lavaflex', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X']
Number of selected features: 10
Selected features 9, top 5: ['PULSE_280V_Z1_LOW', 'EPI_700V_X2_LOW', 'Heat_Sink_Temp_Amplifier_Lavaflex', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X']
Number of selected features: 10
Selected features 9, top 5: ['PULSE_280V_Z1_LOW', 'EPI_700V_X2_LOW', 'Heat_Sink_Temp_Amplifier_Lavaflex', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X']
Number of selected features: 10
Selected features 9, top 5: ['PULSE_280V_Z1_LOW', 'EPI_700V_X2_LOW', 'Heat_Sink_Temp_Amplifier_Lavaflex', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X']
Number of selected features: 10
Selected features 9, top 5: ['OUTPUT_VOLTAGE_DISCH_V700_H_Y', '300V_V700_L_X', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'EPI_700V_Z2_HIGH']
Number of selected features: 10
Selected features 9, top 5: ['OUTPUT_VOLTAGE_DISCH_V700_H_Y', '300V_V700_L_X', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'EPI_700V_Z2_HIGH']
Number of selected features: 10
Selected features 9, top 5: ['OUTPUT_VOLTAGE_DISCH_V700_H_Y', '300V_V700_L_X', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'EPI_700V_Z2_HIGH']
Number of selected features: 10
Selected features 9, top 5: ['OUTPUT_VOLTAGE_DISCH_V700_H_Y', '300V_V700_L_X', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'EPI_700V_Z2_HIGH']
Number of selected features: 10
Selected features 9, top 5: ['OUTPUT_VOLTAGE_DISCH_V700_H_Y', '300V_V700_L_X', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'EPI_700V_Z2_HIGH']
Number of selected features: 10
Selected features 9, top 5: ['OUTPUT_VOLTAGE_DISCH_V700_H_Y', '300V_V700_L_X', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'EPI_700V_Z2_HIGH']
Number of selected features: 10
Selected features 9, top 5: ['OUTPUT_VOLTAGE_DISCH_V700_H_Y', '300V_V700_L_X', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'EPI_700V_Z2_HIGH']
Number of selected features: 10
Selected features 9, top 5: ['OUTPUT_VOLTAGE_DISCH_V700_H_Y', '300V_V700_L_X', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'EPI_700V_Z2_HIGH']
Number of selected features: 10
Selected features 9, top 5: ['OUTPUT_VOLTAGE_DISCH_V700_H_Y', '300V_V700_L_X', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'EPI_700V_Z2_HIGH']
Number of selected features: 10
Selected features 9, top 5: ['OUTPUT_VOLTAGE_DISCH_V700_H_Y', '300V_V700_L_X', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'EPI_700V_Z2_HIGH']
Number of selected features: 10
Selected features 9, top 5: ['Z_CLOSED_LOOP_V280_L_Y', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y', 'EPI_700V_Y2_LOW', 'EPI_280V_X1_LOW']
Number of selected features: 10
Selected features 9, top 5: ['Z_CLOSED_LOOP_V280_L_Y', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y', 'EPI_700V_Y2_LOW', 'EPI_280V_X1_LOW']
Number of selected features: 10
Selected features 9, top 5: ['Z_CLOSED_LOOP_V280_L_Y', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y', 'EPI_700V_Y2_LOW', 'EPI_280V_X1_LOW']2025-07-22 09:54:30,472 - INFO: Fold 3 Test C-index: 0.5977
2025-07-22 09:54:30,472 - INFO: Fold 3: New best CV model with fold validation C-index: 0.5726
2025-07-22 09:54:30,472 - INFO: --- Fold 4/10 ---
2025-07-22 09:54:30,478 - INFO: Features for this fold have been standardized.
2025-07-22 09:54:30,479 - INFO: Using SRDO reweighting
2025-07-22 09:54:36,598 - INFO: SRDO reweighting completed.
2025-07-22 09:54:36,599 - INFO: Weights calculated and normalized for fold 4.
2025-07-22 09:54:36,599 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.034378
2025-07-22 09:54:36,599 - INFO: After normalization - max: 16.638910, min: 0.000000
2025-07-22 09:54:36,599 - INFO: W before clip: min=0.000000, max=16.638910, mean=1.000000, std=1.597788
2025-07-22 09:54:36,599 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.804793, std=0.879740
2025-07-22 09:54:36,599 - INFO: Starting regression paradigm
2025-07-22 09:54:36,599 - INFO: Training backend model 'LogNormal' on fold 4...
2025-07-22 09:54:36,599 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:54:36,947 - INFO: Fold 4 Training C-index: 0.5318
2025-07-22 09:54:37,297 - INFO: Fold 4 Test C-index: 0.4941
2025-07-22 09:54:37,298 - INFO: --- Fold 5/10 ---
2025-07-22 09:54:37,303 - INFO: Features for this fold have been standardized.
2025-07-22 09:54:37,304 - INFO: Using SRDO reweighting
2025-07-22 09:54:43,218 - INFO: SRDO reweighting completed.
2025-07-22 09:54:43,219 - INFO: Weights calculated and normalized for fold 5.
2025-07-22 09:54:43,219 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.039828
2025-07-22 09:54:43,219 - INFO: After normalization - max: 19.276897, min: 0.000000
2025-07-22 09:54:43,219 - INFO: W before clip: min=0.000000, max=19.276897, mean=1.000000, std=1.740396
2025-07-22 09:54:43,219 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.766326, std=0.863039
2025-07-22 09:54:43,219 - INFO: Starting regression paradigm
2025-07-22 09:54:43,220 - INFO: Training backend model 'LogNormal' on fold 5...
2025-07-22 09:54:43,220 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:54:43,487 - INFO: Fold 5 Training C-index: 0.5287
2025-07-22 09:54:43,814 - INFO: Fold 5 Test C-index: 0.4487
2025-07-22 09:54:43,814 - INFO: --- Fold 6/10 ---
2025-07-22 09:54:43,823 - INFO: Features for this fold have been standardized.
2025-07-22 09:54:43,824 - INFO: Using SRDO reweighting
2025-07-22 09:54:49,251 - INFO: SRDO reweighting completed.
2025-07-22 09:54:49,251 - INFO: Weights calculated and normalized for fold 6.
2025-07-22 09:54:49,251 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.028500
2025-07-22 09:54:49,251 - INFO: After normalization - max: 13.793921, min: 0.000000
2025-07-22 09:54:49,251 - INFO: W before clip: min=0.000000, max=13.793921, mean=1.000000, std=1.386525
2025-07-22 09:54:49,251 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.847161, std=0.870094
2025-07-22 09:54:49,251 - INFO: Starting regression paradigm
2025-07-22 09:54:49,251 - INFO: Training backend model 'LogNormal' on fold 6...
2025-07-22 09:54:49,251 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:54:49,590 - INFO: Fold 6 Training C-index: 0.5471
2025-07-22 09:54:49,956 - INFO: Fold 6 Test C-index: 0.3084
2025-07-22 09:54:49,956 - INFO: --- Fold 7/10 ---
2025-07-22 09:54:49,963 - INFO: Features for this fold have been standardized.
2025-07-22 09:54:49,964 - INFO: Using SRDO reweighting
2025-07-22 09:54:56,413 - INFO: SRDO reweighting completed.
2025-07-22 09:54:56,413 - INFO: Weights calculated and normalized for fold 7.
2025-07-22 09:54:56,413 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.018405
2025-07-22 09:54:56,413 - INFO: After normalization - max: 8.908236, min: 0.000000
2025-07-22 09:54:56,413 - INFO: W before clip: min=0.000000, max=8.908236, mean=1.000000, std=1.424121
2025-07-22 09:54:56,414 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.809804, std=0.874625
2025-07-22 09:54:56,414 - INFO: Starting regression paradigm
2025-07-22 09:54:56,414 - INFO: Training backend model 'LogNormal' on fold 7...
2025-07-22 09:54:56,414 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:54:56,681 - INFO: Fold 7 Training C-index: 0.5198
2025-07-22 09:54:57,001 - INFO: Fold 7 Test C-index: 0.6049
2025-07-22 09:54:57,002 - INFO: Fold 7: New best CV model with fold validation C-index: 0.5794
2025-07-22 09:54:57,002 - INFO: --- Fold 8/10 ---
2025-07-22 09:54:57,006 - INFO: Features for this fold have been standardized.
2025-07-22 09:54:57,006 - INFO: Using SRDO reweighting
2025-07-22 09:55:03,017 - INFO: SRDO reweighting completed.
2025-07-22 09:55:03,017 - INFO: Weights calculated and normalized for fold 8.
2025-07-22 09:55:03,017 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.020373
2025-07-22 09:55:03,017 - INFO: After normalization - max: 9.860568, min: 0.000000
2025-07-22 09:55:03,018 - INFO: W before clip: min=0.000000, max=9.860568, mean=1.000000, std=1.438531
2025-07-22 09:55:03,018 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.819481, std=0.919367
2025-07-22 09:55:03,018 - INFO: Starting regression paradigm
2025-07-22 09:55:03,018 - INFO: Training backend model 'LogNormal' on fold 8...
2025-07-22 09:55:03,018 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:55:03,380 - INFO: Fold 8 Training C-index: 0.5171
2025-07-22 09:55:03,628 - INFO: Fold 8 Test C-index: 0.6026
2025-07-22 09:55:03,628 - INFO: --- Fold 9/10 ---
2025-07-22 09:55:03,631 - INFO: Features for this fold have been standardized.
2025-07-22 09:55:03,632 - INFO: Using SRDO reweighting
2025-07-22 09:55:08,747 - INFO: SRDO reweighting completed.
2025-07-22 09:55:08,748 - INFO: Weights calculated and normalized for fold 9.
2025-07-22 09:55:08,748 - INFO: Weight statistics - mean: 0.002062, min: 0.000000, max: 0.017530
2025-07-22 09:55:08,748 - INFO: After normalization - max: 8.502004, min: 0.000000
2025-07-22 09:55:08,748 - INFO: W before clip: min=0.000000, max=8.502004, mean=1.000000, std=1.294981
2025-07-22 09:55:08,748 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.855698, std=0.881826
2025-07-22 09:55:08,748 - INFO: Starting regression paradigm
2025-07-22 09:55:08,749 - INFO: Training backend model 'LogNormal' on fold 9...
2025-07-22 09:55:08,749 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:55:09,190 - INFO: Fold 9 Training C-index: 0.5144
2025-07-22 09:55:09,540 - INFO: Fold 9 Test C-index: 0.5732
2025-07-22 09:55:09,541 - INFO: --- Fold 10/10 ---
2025-07-22 09:55:09,547 - INFO: Features for this fold have been standardized.
2025-07-22 09:55:09,548 - INFO: Using SRDO reweighting
2025-07-22 09:55:14,375 - INFO: SRDO reweighting completed.
2025-07-22 09:55:14,375 - INFO: Weights calculated and normalized for fold 10.
2025-07-22 09:55:14,375 - INFO: Weight statistics - mean: 0.002062, min: 0.000000, max: 0.015173
2025-07-22 09:55:14,375 - INFO: After normalization - max: 7.358675, min: 0.000000
2025-07-22 09:55:14,375 - INFO: W before clip: min=0.000000, max=7.358675, mean=1.000000, std=1.317118
2025-07-22 09:55:14,375 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.852010, std=0.932701
2025-07-22 09:55:14,375 - INFO: Starting regression paradigm
2025-07-22 09:55:14,375 - INFO: Training backend model 'LogNormal' on fold 10...
2025-07-22 09:55:14,375 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:55:14,621 - INFO: Fold 10 Training C-index: 0.5347
2025-07-22 09:55:14,897 - INFO: Fold 10 Test C-index: 0.4384
2025-07-22 09:55:14,904 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 5 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-22 09:55:14,904 - INFO: --- Starting Round 5 with Seed 7 ---
2025-07-22 09:55:14,925 - INFO: Successfully loaded data from processed_equipment_data_new.csv. Shape: (598, 187)
2025-07-22 09:55:14,925 - INFO: Succsessfully get feature columns. Total: 180
2025-07-22 09:55:14,930 - INFO: --- Fold 1/10 ---
2025-07-22 09:55:14,953 - INFO: Features for this fold have been standardized.
2025-07-22 09:55:14,956 - INFO: Using SRDO reweighting
2025-07-22 09:55:15,733 - INFO: SRDO reweighting completed.
2025-07-22 09:55:15,733 - INFO: Weights calculated and normalized for fold 1.
2025-07-22 09:55:15,734 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.017660
2025-07-22 09:55:15,734 - INFO: After normalization - max: 8.547257, min: 0.000000
2025-07-22 09:55:15,734 - INFO: W before clip: min=0.000000, max=8.547257, mean=1.000000, std=1.293578
2025-07-22 09:55:15,734 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.865504, std=0.943994
2025-07-22 09:55:15,734 - INFO: Starting regression paradigm
2025-07-22 09:55:15,734 - INFO: Training backend model 'LogNormal' on fold 1...
2025-07-22 09:55:15,735 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:55:16,653 - INFO: Fold 1 Training C-index: 0.5092
2025-07-22 09:55:16,971 - INFO: Fold 1 Test C-index: 0.4767
2025-07-22 09:55:16,971 - INFO: Fold 1: New best CV model with fold validation C-index: 0.4852
2025-07-22 09:55:16,971 - INFO: --- Fold 2/10 ---
2025-07-22 09:55:16,976 - INFO: Features for this fold have been standardized.
2025-07-22 09:55:16,977 - INFO: Using SRDO reweighting
2025-07-22 09:55:20,870 - INFO: SRDO reweighting completed.
2025-07-22 09:55:20,870 - INFO: Weights calculated and normalized for fold 2.
2025-07-22 09:55:20,870 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.029649
2025-07-22 09:55:20,870 - INFO: After normalization - max: 14.349925, min: 0.000000
2025-07-22 09:55:20,870 - INFO: W before clip: min=0.000000, max=14.349925, mean=1.000000, std=1.530794
2025-07-22 09:55:20,870 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.805469, std=0.916784
2025-07-22 09:55:20,870 - INFO: Starting regression paradigm
2025-07-22 09:55:20,870 - INFO: Training backend model 'LogNormal' on fold 2...
2025-07-22 09:55:20,870 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:55:21,119 - INFO: Fold 2 Training C-index: 0.5150
2025-07-22 09:55:21,296 - INFO: Fold 2 Test C-index: 0.3974
2025-07-22 09:55:21,296 - INFO: --- Fold 3/10 ---
2025-07-22 09:55:21,299 - INFO: Features for this fold have been standardized.
2025-07-22 09:55:21,300 - INFO: Using SRDO reweighting
2025-07-22 09:55:27,604 - INFO: SRDO reweighting completed.
2025-07-22 09:55:27,605 - INFO: Weights calculated and normalized for fold 3.
2025-07-22 09:55:27,605 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.019329
2025-07-22 09:55:27,605 - INFO: After normalization - max: 9.355301, min: 0.000000
2025-07-22 09:55:27,605 - INFO: W before clip: min=0.000000, max=9.355301, mean=1.000000, std=1.408660
2025-07-22 09:55:27,605 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.824635, std=0.922286
2025-07-22 09:55:27,605 - INFO: Starting regression paradigm
2025-07-22 09:55:27,606 - INFO: Training backend model 'LogNormal' on fold 3...
2025-07-22 09:55:27,606 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:55:27,875 - INFO: Fold 3 Training C-index: 0.4940
2025-07-22 09:55:28,135 - INFO: Fold 3 Test C-index: 0.5412
2025-07-22 09:55:28,135 - INFO: Fold 3: New best CV model with fold validation C-index: 0.5270
2025-07-22 09:55:28,135 - INFO: --- Fold 4/10 ---
2025-07-22 09:55:28,141 - INFO: Features for this fold have been standardized.
2025-07-22 09:55:28,142 - INFO: Using SRDO reweighting
2025-07-22 09:55:32,603 - INFO: SRDO reweighting completed.
2025-07-22 09:55:32,603 - INFO: Weights calculated and normalized for fold 4.
2025-07-22 09:55:32,603 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.015260
2025-07-22 09:55:32,603 - INFO: After normalization - max: 7.385840, min: 0.000000
2025-07-22 09:55:32,603 - INFO: W before clip: min=0.000000, max=7.385840, mean=1.000000, std=1.387797
2025-07-22 09:55:32,603 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.816625, std=0.914404
2025-07-22 09:55:32,603 - INFO: Starting regression paradigm
2025-07-22 09:55:32,603 - INFO: Training backend model 'LogNormal' on fold 4...
2025-07-22 09:55:32,603 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:55:32,854 - INFO: Fold 4 Training C-index: 0.4964
2025-07-22 09:55:33,117 - INFO: Fold 4 Test C-index: 0.6024
2025-07-22 09:55:33,117 - INFO: Fold 4: New best CV model with fold validation C-index: 0.5706
2025-07-22 09:55:33,118 - INFO: --- Fold 5/10 ---
2025-07-22 09:55:33,122 - INFO: Features for this fold have been standardized.
2025-07-22 09:55:33,123 - INFO: Using SRDO reweighting
2025-07-22 09:55:38,534 - INFO: SRDO reweighting completed.
2025-07-22 09:55:38,534 - INFO: Weights calculated and normalized for fold 5.
2025-07-22 09:55:38,534 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.017983
2025-07-22 09:55:38,535 - INFO: After normalization - max: 8.703643, min: 0.000000
2025-07-22 09:55:38,535 - INFO: W before clip: min=0.000000, max=8.703643, mean=1.000000, std=1.360896
2025-07-22 09:55:38,535 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.835713, std=0.877016
2025-07-22 09:55:38,535 - INFO: Starting regression paradigm
2025-07-22 09:55:38,535 - INFO: Training backend model 'LogNormal' on fold 5...
2025-07-22 09:55:38,535 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:55:39,031 - INFO: Fold 5 Training C-index: 0.5276
2025-07-22 09:55:39,385 - INFO: Fold 5 Test C-index: 0.3125
2025-07-22 09:55:39,385 - INFO: --- Fold 6/10 ---
2025-07-22 09:55:39,389 - INFO: Features for this fold have been standardized.
2025-07-22 09:55:39,389 - INFO: Using SRDO reweighting
2025-07-22 09:55:44,016 - INFO: SRDO reweighting completed.
2025-07-22 09:55:44,016 - INFO: Weights calculated and normalized for fold 6.
2025-07-22 09:55:44,016 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.017347
2025-07-22 09:55:44,016 - INFO: After normalization - max: 8.396078, min: 0.000000
2025-07-22 09:55:44,017 - INFO: W before clip: min=0.000000, max=8.396078, mean=1.000000, std=1.327722
2025-07-22 09:55:44,017 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.845630, std=0.898528
2025-07-22 09:55:44,017 - INFO: Starting regression paradigm
2025-07-22 09:55:44,017 - INFO: Training backend model 'LogNormal' on fold 6...
2025-07-22 09:55:44,017 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:55:44,199 - INFO: Fold 6 Training C-index: 0.4943
2025-07-22 09:55:44,389 - INFO: Fold 6 Test C-index: 0.5977
2025-07-22 09:55:44,389 - INFO: --- Fold 7/10 ---
2025-07-22 09:55:44,393 - INFO: Features for this fold have been standardized.
2025-07-22 09:55:44,393 - INFO: Using SRDO reweighting
2025-07-22 09:55:53,568 - INFO: SRDO reweighting completed.
2025-07-22 09:55:53,568 - INFO: Weights calculated and normalized for fold 7.
2025-07-22 09:55:53,569 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.018904
2025-07-22 09:55:53,569 - INFO: After normalization - max: 9.149734, min: 0.000000
2025-07-22 09:55:53,570 - INFO: W before clip: min=0.000000, max=9.149734, mean=1.000000, std=1.325634
2025-07-22 09:55:53,570 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.842406, std=0.917108
2025-07-22 09:55:53,571 - INFO: Starting regression paradigm
2025-07-22 09:55:53,571 - INFO: Training backend model 'LogNormal' on fold 7...
2025-07-22 09:55:53,571 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:55:54,859 - INFO: Fold 7 Training C-index: 0.4896
2025-07-22 09:55:56,295 - INFO: Fold 7 Test C-index: 0.5747
2025-07-22 09:55:56,295 - INFO: --- Fold 8/10 ---
2025-07-22 09:55:56,318 - INFO: Features for this fold have been standardized.
2025-07-22 09:55:56,321 - INFO: Using SRDO reweighting
2025-07-22 09:56:12,234 - INFO: SRDO reweighting completed.
2025-07-22 09:56:12,235 - INFO: Weights calculated and normalized for fold 8.
2025-07-22 09:56:12,235 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.021758
2025-07-22 09:56:12,235 - INFO: After normalization - max: 10.530809, min: 0.000000
2025-07-22 09:56:12,236 - INFO: W before clip: min=0.000000, max=10.530809, mean=1.000000, std=1.508015
2025-07-22 09:56:12,236 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.796414, std=0.896806
2025-07-22 09:56:12,237 - INFO: Starting regression paradigm
2025-07-22 09:56:12,237 - INFO: Training backend model 'LogNormal' on fold 8...
2025-07-22 09:56:12,237 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:56:13,617 - INFO: Fold 8 Training C-index: 0.5187
2025-07-22 09:56:15,019 - INFO: Fold 8 Test C-index: 0.4268
2025-07-22 09:56:15,020 - INFO: --- Fold 9/10 ---
2025-07-22 09:56:15,043 - INFO: Features for this fold have been standardized.
2025-07-22 09:56:15,046 - INFO: Using SRDO reweighting
2025-07-22 09:56:29,611 - INFO: SRDO reweighting completed.
2025-07-22 09:56:29,611 - INFO: Weights calculated and normalized for fold 9.
2025-07-22 09:56:29,611 - INFO: Weight statistics - mean: 0.002062, min: 0.000000, max: 0.024229
2025-07-22 09:56:29,612 - INFO: After normalization - max: 11.750873, min: 0.000000
2025-07-22 09:56:29,612 - INFO: W before clip: min=0.000000, max=11.750873, mean=1.000000, std=1.370867
2025-07-22 09:56:29,613 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.832323, std=0.894586
2025-07-22 09:56:29,613 - INFO: Starting regression paradigm
2025-07-22 09:56:29,613 - INFO: Training backend model 'LogNormal' on fold 9...
2025-07-22 09:56:29,614 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:56:30,971 - INFO: Fold 9 Training C-index: 0.4980
2025-07-22 09:56:32,431 - INFO: Fold 9 Test C-index: 0.4848
2025-07-22 09:56:32,432 - INFO: --- Fold 10/10 ---
2025-07-22 09:56:32,457 - INFO: Features for this fold have been standardized.
2025-07-22 09:56:32,459 - INFO: Using SRDO reweighting
2025-07-22 09:56:48,602 - INFO: SRDO reweighting completed.
2025-07-22 09:56:48,602 - INFO: Weights calculated and normalized for fold 10.
2025-07-22 09:56:48,602 - INFO: Weight statistics - mean: 0.002062, min: 0.000000, max: 0.024926
2025-07-22 09:56:48,602 - INFO: After normalization - max: 12.089149, min: 0.000000
2025-07-22 09:56:48,602 - INFO: W before clip: min=0.000000, max=12.089149, mean=1.000000, std=1.509107
2025-07-22 09:56:48,603 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.804723, std=0.892615
2025-07-22 09:56:48,603 - INFO: Starting regression paradigm
2025-07-22 09:56:48,603 - INFO: Training backend model 'LogNormal' on fold 10...
2025-07-22 09:56:48,603 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:56:49,302 - INFO: Fold 10 Training C-index: 0.5038
2025-07-22 09:56:49,766 - INFO: Fold 10 Test C-index: 0.5747
2025-07-22 09:56:49,776 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 6 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-22 09:56:49,776 - INFO: --- Starting Round 6 with Seed 8 ---
2025-07-22 09:56:49,805 - INFO: Successfully loaded data from processed_equipment_data_new.csv. Shape: (598, 187)
2025-07-22 09:56:49,807 - INFO: Succsessfully get feature columns. Total: 180
2025-07-22 09:56:49,810 - INFO: --- Fold 1/10 ---
2025-07-22 09:56:49,848 - INFO: Features for this fold have been standardized.
2025-07-22 09:56:49,854 - INFO: Using SRDO reweighting
2025-07-22 09:56:54,224 - INFO: SRDO reweighting completed.
2025-07-22 09:56:54,224 - INFO: Weights calculated and normalized for fold 1.
2025-07-22 09:56:54,225 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.014343
2025-07-22 09:56:54,225 - INFO: After normalization - max: 6.942179, min: 0.000000
2025-07-22 09:56:54,226 - INFO: W before clip: min=0.000000, max=6.942179, mean=1.000000, std=1.219995
2025-07-22 09:56:54,226 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.878423, std=0.928594
2025-07-22 09:56:54,227 - INFO: Starting regression paradigm
2025-07-22 09:56:54,227 - INFO: Training backend model 'LogNormal' on fold 1...
2025-07-22 09:56:54,227 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:56:57,384 - INFO: Fold 1 Training C-index: 0.5152
2025-07-22 09:56:58,649 - INFO: Fold 1 Test C-index: 0.5000
2025-07-22 09:56:58,649 - INFO: Fold 1: New best CV model with fold validation C-index: 0.5048
2025-07-22 09:56:58,649 - INFO: --- Fold 2/10 ---
2025-07-22 09:56:58,671 - INFO: Features for this fold have been standardized.
2025-07-22 09:56:58,673 - INFO: Using SRDO reweighting
2025-07-22 09:57:13,810 - INFO: SRDO reweighting completed.
2025-07-22 09:57:13,810 - INFO: Weights calculated and normalized for fold 2.
2025-07-22 09:57:13,811 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.015952
2025-07-22 09:57:13,811 - INFO: After normalization - max: 7.720999, min: 0.000000
2025-07-22 09:57:13,812 - INFO: W before clip: min=0.000000, max=7.720999, mean=1.000000, std=1.298158
2025-07-22 09:57:13,812 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.846215, std=0.886406
2025-07-22 09:57:13,813 - INFO: Starting regression paradigm
2025-07-22 09:57:13,813 - INFO: Training backend model 'LogNormal' on fold 2...
2025-07-22 09:57:13,813 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:57:14,851 - INFO: Fold 2 Training C-index: 0.5052
2025-07-22 09:57:15,540 - INFO: Fold 2 Test C-index: 0.4176
2025-07-22 09:57:15,540 - INFO: --- Fold 3/10 ---
2025-07-22 09:57:15,559 - INFO: Features for this fold have been standardized.
2025-07-22 09:57:15,562 - INFO: Using SRDO reweighting
2025-07-22 09:58:01,078 - INFO: SRDO reweighting completed.
2025-07-22 09:58:01,079 - INFO: Weights calculated and normalized for fold 3.
2025-07-22 09:58:01,079 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.029248
2025-07-22 09:58:01,079 - INFO: After normalization - max: 14.155811, min: 0.000000
2025-07-22 09:58:01,079 - INFO: W before clip: min=0.000000, max=14.155811, mean=1.000000, std=1.490608
2025-07-22 09:58:01,079 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.813832, std=0.882286
2025-07-22 09:58:01,079 - INFO: Starting regression paradigm
2025-07-22 09:58:01,080 - INFO: Training backend model 'LogNormal' on fold 3...
2025-07-22 09:58:01,080 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:58:01,489 - INFO: Fold 3 Training C-index: 0.5223
2025-07-22 09:58:01,867 - INFO: Fold 3 Test C-index: 0.3974
2025-07-22 09:58:01,867 - INFO: --- Fold 4/10 ---
2025-07-22 09:58:01,872 - INFO: Features for this fold have been standardized.
2025-07-22 09:58:01,873 - INFO: Using SRDO reweighting
2025-07-22 09:58:07,235 - INFO: SRDO reweighting completed.
2025-07-22 09:58:07,236 - INFO: Weights calculated and normalized for fold 4.
2025-07-22 09:58:07,236 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.020193
2025-07-22 09:58:07,236 - INFO: After normalization - max: 9.773533, min: 0.000000
2025-07-22 09:58:07,236 - INFO: W before clip: min=0.000000, max=9.773533, mean=1.000000, std=1.413137
2025-07-22 09:58:07,237 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.822172, std=0.899838
2025-07-22 09:58:07,237 - INFO: Starting regression paradigm
2025-07-22 09:58:07,237 - INFO: Training backend model 'LogNormal' on fold 4...
2025-07-22 09:58:07,237 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:58:07,633 - INFO: Fold 4 Training C-index: 0.4982
2025-07-22 09:58:08,044 - INFO: Fold 4 Test C-index: 0.5696
2025-07-22 09:58:08,045 - INFO: Fold 4: New best CV model with fold validation C-index: 0.5482
2025-07-22 09:58:08,045 - INFO: --- Fold 5/10 ---
2025-07-22 09:58:08,051 - INFO: Features for this fold have been standardized.
2025-07-22 09:58:08,052 - INFO: Using SRDO reweighting
2025-07-22 09:58:15,179 - INFO: SRDO reweighting completed.
2025-07-22 09:58:15,179 - INFO: Weights calculated and normalized for fold 5.
2025-07-22 09:58:15,179 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.031779
2025-07-22 09:58:15,179 - INFO: After normalization - max: 15.381009, min: 0.000000
2025-07-22 09:58:15,179 - INFO: W before clip: min=0.000000, max=15.381009, mean=1.000000, std=1.466007
2025-07-22 09:58:15,179 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.832836, std=0.865632
2025-07-22 09:58:15,179 - INFO: Starting regression paradigm
2025-07-22 09:58:15,179 - INFO: Training backend model 'LogNormal' on fold 5...
2025-07-22 09:58:15,180 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:58:15,542 - INFO: Fold 5 Training C-index: 0.5221
2025-07-22 09:58:15,903 - INFO: Fold 5 Test C-index: 0.4167
2025-07-22 09:58:15,903 - INFO: --- Fold 6/10 ---
2025-07-22 09:58:15,913 - INFO: Features for this fold have been standardized.
2025-07-22 09:58:15,914 - INFO: Using SRDO reweighting
2025-07-22 09:58:21,185 - INFO: SRDO reweighting completed.
2025-07-22 09:58:21,185 - INFO: Weights calculated and normalized for fold 6.
2025-07-22 09:58:21,186 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.016017
2025-07-22 09:58:21,186 - INFO: After normalization - max: 7.752198, min: 0.000000
2025-07-22 09:58:21,186 - INFO: W before clip: min=0.000000, max=7.752198, mean=1.000000, std=1.316669
2025-07-22 09:58:21,186 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.847489, std=0.899731
2025-07-22 09:58:21,186 - INFO: Starting regression paradigm
2025-07-22 09:58:21,187 - INFO: Training backend model 'LogNormal' on fold 6...
2025-07-22 09:58:21,187 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:58:21,522 - INFO: Fold 6 Training C-index: 0.4929
2025-07-22 09:58:21,884 - INFO: Fold 6 Test C-index: 0.6437
2025-07-22 09:58:21,884 - INFO: Fold 6: New best CV model with fold validation C-index: 0.5984
2025-07-22 09:58:21,884 - INFO: --- Fold 7/10 ---
2025-07-22 09:58:21,890 - INFO: Features for this fold have been standardized.
2025-07-22 09:58:21,890 - INFO: Using SRDO reweighting
2025-07-22 09:58:27,130 - INFO: SRDO reweighting completed.
2025-07-22 09:58:27,130 - INFO: Weights calculated and normalized for fold 7.
2025-07-22 09:58:27,130 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.015693
2025-07-22 09:58:27,130 - INFO: After normalization - max: 7.595173, min: 0.000000
2025-07-22 09:58:27,131 - INFO: W before clip: min=0.000000, max=7.595173, mean=1.000000, std=1.266290
2025-07-22 09:58:27,131 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.870352, std=0.934476
2025-07-22 09:58:27,131 - INFO: Starting regression paradigm
2025-07-22 09:58:27,131 - INFO: Training backend model 'LogNormal' on fold 7...
2025-07-22 09:58:27,131 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:58:27,466 - INFO: Fold 7 Training C-index: 0.4946
2025-07-22 09:58:27,857 - INFO: Fold 7 Test C-index: 0.6207
2025-07-22 09:58:27,857 - INFO: --- Fold 8/10 ---
2025-07-22 09:58:27,863 - INFO: Features for this fold have been standardized.
2025-07-22 09:58:27,864 - INFO: Using SRDO reweighting
2025-07-22 09:58:32,696 - INFO: SRDO reweighting completed.
2025-07-22 09:58:32,697 - INFO: Weights calculated and normalized for fold 8.
2025-07-22 09:58:32,697 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.024736
2025-07-22 09:58:32,697 - INFO: After normalization - max: 11.972111, min: 0.000000
2025-07-22 09:58:32,697 - INFO: W before clip: min=0.000000, max=11.972111, mean=1.000000, std=1.465619
2025-07-22 09:58:32,697 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.811534, std=0.909050
2025-07-22 09:58:32,698 - INFO: Starting regression paradigm
2025-07-22 09:58:32,698 - INFO: Training backend model 'LogNormal' on fold 8...
2025-07-22 09:58:32,698 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:58:33,088 - INFO: Fold 8 Training C-index: 0.5251
2025-07-22 09:58:33,436 - INFO: Fold 8 Test C-index: 0.4824
2025-07-22 09:58:33,436 - INFO: --- Fold 9/10 ---
2025-07-22 09:58:33,443 - INFO: Features for this fold have been standardized.
2025-07-22 09:58:33,444 - INFO: Using SRDO reweighting
2025-07-22 09:58:39,402 - INFO: SRDO reweighting completed.
2025-07-22 09:58:39,402 - INFO: Weights calculated and normalized for fold 9.
2025-07-22 09:58:39,402 - INFO: Weight statistics - mean: 0.002062, min: 0.000000, max: 0.021695
2025-07-22 09:58:39,402 - INFO: After normalization - max: 10.521862, min: 0.000000
2025-07-22 09:58:39,402 - INFO: W before clip: min=0.000000, max=10.521862, mean=1.000000, std=1.477267
2025-07-22 09:58:39,403 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.810505, std=0.896840
2025-07-22 09:58:39,403 - INFO: Starting regression paradigm
2025-07-22 09:58:39,403 - INFO: Training backend model 'LogNormal' on fold 9...
2025-07-22 09:58:39,403 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:58:39,721 - INFO: Fold 9 Training C-index: 0.5031
2025-07-22 09:58:40,058 - INFO: Fold 9 Test C-index: 0.5942
2025-07-22 09:58:40,059 - INFO: --- Fold 10/10 ---
2025-07-22 09:58:40,066 - INFO: Features for this fold have been standardized.
2025-07-22 09:58:40,066 - INFO: Using SRDO reweighting
2025-07-22 09:58:45,559 - INFO: SRDO reweighting completed.
2025-07-22 09:58:45,560 - INFO: Weights calculated and normalized for fold 10.
2025-07-22 09:58:45,560 - INFO: Weight statistics - mean: 0.002062, min: 0.000000, max: 0.020178
2025-07-22 09:58:45,560 - INFO: After normalization - max: 9.786361, min: 0.000000
2025-07-22 09:58:45,560 - INFO: W before clip: min=0.000000, max=9.786361, mean=1.000000, std=1.388240
2025-07-22 09:58:45,560 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.827025, std=0.913439
2025-07-22 09:58:45,560 - INFO: Starting regression paradigm
2025-07-22 09:58:45,560 - INFO: Training backend model 'LogNormal' on fold 10...
2025-07-22 09:58:45,560 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:58:45,945 - INFO: Fold 10 Training C-index: 0.5050
2025-07-22 09:58:46,311 - INFO: Fold 10 Test C-index: 0.5493
2025-07-22 09:58:46,321 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 7 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-22 09:58:46,321 - INFO: --- Starting Round 7 with Seed 9 ---
2025-07-22 09:58:46,345 - INFO: Successfully loaded data from processed_equipment_data_new.csv. Shape: (598, 187)
2025-07-22 09:58:46,346 - INFO: Succsessfully get feature columns. Total: 180
2025-07-22 09:58:46,350 - INFO: --- Fold 1/10 ---
2025-07-22 09:58:46,384 - INFO: Features for this fold have been standardized.
2025-07-22 09:58:46,388 - INFO: Using SRDO reweighting
2025-07-22 09:58:47,385 - INFO: SRDO reweighting completed.
2025-07-22 09:58:47,385 - INFO: Weights calculated and normalized for fold 1.
2025-07-22 09:58:47,385 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.013086
2025-07-22 09:58:47,386 - INFO: After normalization - max: 6.333603, min: 0.000000
2025-07-22 09:58:47,386 - INFO: W before clip: min=0.000000, max=6.333603, mean=1.000000, std=1.188758
2025-07-22 09:58:47,386 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.897629, std=0.952989
2025-07-22 09:58:47,386 - INFO: Starting regression paradigm
2025-07-22 09:58:47,386 - INFO: Training backend model 'LogNormal' on fold 1...
2025-07-22 09:58:47,386 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:58:48,545 - INFO: Fold 1 Training C-index: 0.5061
2025-07-22 09:58:49,092 - INFO: Fold 1 Test C-index: 0.4643
2025-07-22 09:58:49,092 - INFO: Fold 1: New best CV model with fold validation C-index: 0.4767
2025-07-22 09:58:49,092 - INFO: --- Fold 2/10 ---
2025-07-22 09:58:49,101 - INFO: Features for this fold have been standardized.
2025-07-22 09:58:49,102 - INFO: Using SRDO reweighting
2025-07-22 09:58:54,629 - INFO: SRDO reweighting completed.
2025-07-22 09:58:54,629 - INFO: Weights calculated and normalized for fold 2.
2025-07-22 09:58:54,629 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.024325
2025-07-22 09:58:54,629 - INFO: After normalization - max: 11.773063, min: 0.000000
2025-07-22 09:58:54,629 - INFO: W before clip: min=0.000000, max=11.773063, mean=1.000000, std=1.465958
2025-07-22 09:58:54,630 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.815813, std=0.879268
2025-07-22 09:58:54,630 - INFO: Starting regression paradigm
2025-07-22 09:58:54,630 - INFO: Training backend model 'LogNormal' on fold 2...
2025-07-22 09:58:54,630 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:58:54,972 - INFO: Fold 2 Training C-index: 0.4915
2025-07-22 09:58:55,372 - INFO: Fold 2 Test C-index: 0.6548
2025-07-22 09:58:55,373 - INFO: Fold 2: New best CV model with fold validation C-index: 0.6058
2025-07-22 09:58:55,373 - INFO: --- Fold 3/10 ---
2025-07-22 09:58:55,380 - INFO: Features for this fold have been standardized.
2025-07-22 09:58:55,381 - INFO: Using SRDO reweighting
2025-07-22 09:59:01,151 - INFO: SRDO reweighting completed.
2025-07-22 09:59:01,151 - INFO: Weights calculated and normalized for fold 3.
2025-07-22 09:59:01,151 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.018515
2025-07-22 09:59:01,151 - INFO: After normalization - max: 8.961350, min: 0.000000
2025-07-22 09:59:01,152 - INFO: W before clip: min=0.000000, max=8.961350, mean=1.000000, std=1.508461
2025-07-22 09:59:01,152 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.785656, std=0.881947
2025-07-22 09:59:01,152 - INFO: Starting regression paradigm
2025-07-22 09:59:01,152 - INFO: Training backend model 'LogNormal' on fold 3...
2025-07-22 09:59:01,152 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:59:01,513 - INFO: Fold 3 Training C-index: 0.5066
2025-07-22 09:59:01,867 - INFO: Fold 3 Test C-index: 0.5875
2025-07-22 09:59:01,868 - INFO: --- Fold 4/10 ---
2025-07-22 09:59:01,874 - INFO: Features for this fold have been standardized.
2025-07-22 09:59:01,875 - INFO: Using SRDO reweighting
2025-07-22 09:59:06,844 - INFO: SRDO reweighting completed.
2025-07-22 09:59:06,844 - INFO: Weights calculated and normalized for fold 4.
2025-07-22 09:59:06,845 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.020040
2025-07-22 09:59:06,845 - INFO: After normalization - max: 9.699405, min: 0.000000
2025-07-22 09:59:06,845 - INFO: W before clip: min=0.000000, max=9.699405, mean=1.000000, std=1.345571
2025-07-22 09:59:06,845 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.841839, std=0.885358
2025-07-22 09:59:06,845 - INFO: Starting regression paradigm
2025-07-22 09:59:06,845 - INFO: Training backend model 'LogNormal' on fold 4...
2025-07-22 09:59:06,845 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:59:07,207 - INFO: Fold 4 Training C-index: 0.5240
2025-07-22 09:59:07,572 - INFO: Fold 4 Test C-index: 0.3556
2025-07-22 09:59:07,572 - INFO: --- Fold 5/10 ---
2025-07-22 09:59:07,579 - INFO: Features for this fold have been standardized.
2025-07-22 09:59:07,579 - INFO: Using SRDO reweighting
2025-07-22 09:59:12,482 - INFO: SRDO reweighting completed.
2025-07-22 09:59:12,482 - INFO: Weights calculated and normalized for fold 5.
2025-07-22 09:59:12,482 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.017030
2025-07-22 09:59:12,482 - INFO: After normalization - max: 8.242374, min: 0.000000
2025-07-22 09:59:12,482 - INFO: W before clip: min=0.000000, max=8.242374, mean=1.000000, std=1.295365
2025-07-22 09:59:12,482 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.860310, std=0.927972
2025-07-22 09:59:12,482 - INFO: Starting regression paradigm
2025-07-22 09:59:12,482 - INFO: Training backend model 'LogNormal' on fold 5...
2025-07-22 09:59:12,482 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:59:12,830 - INFO: Fold 5 Training C-index: 0.5260
2025-07-22 09:59:13,146 - INFO: Fold 5 Test C-index: 0.5217
2025-07-22 09:59:13,146 - INFO: --- Fold 6/10 ---
2025-07-22 09:59:13,151 - INFO: Features for this fold have been standardized.
2025-07-22 09:59:13,152 - INFO: Using SRDO reweighting
2025-07-22 09:59:17,854 - INFO: SRDO reweighting completed.
2025-07-22 09:59:17,854 - INFO: Weights calculated and normalized for fold 6.
2025-07-22 09:59:17,854 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.021473
2025-07-22 09:59:17,854 - INFO: After normalization - max: 10.393168, min: 0.000000
2025-07-22 09:59:17,854 - INFO: W before clip: min=0.000000, max=10.393168, mean=1.000000, std=1.342789
2025-07-22 09:59:17,855 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.841010, std=0.870741
2025-07-22 09:59:17,855 - INFO: Starting regression paradigm
2025-07-22 09:59:17,855 - INFO: Training backend model 'LogNormal' on fold 6...
2025-07-22 09:59:17,855 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:59:18,170 - INFO: Fold 6 Training C-index: 0.5064
2025-07-22 09:59:18,477 - INFO: Fold 6 Test C-index: 0.6977
2025-07-22 09:59:18,478 - INFO: Fold 6: New best CV model with fold validation C-index: 0.6403
2025-07-22 09:59:18,478 - INFO: --- Fold 7/10 ---
2025-07-22 09:59:18,486 - INFO: Features for this fold have been standardized.
2025-07-22 09:59:18,487 - INFO: Using SRDO reweighting
2025-07-22 09:59:23,159 - INFO: SRDO reweighting completed.
2025-07-22 09:59:23,159 - INFO: Weights calculated and normalized for fold 7.
2025-07-22 09:59:23,160 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.026387
2025-07-22 09:59:23,160 - INFO: After normalization - max: 12.771266, min: 0.000000
2025-07-22 09:59:23,160 - INFO: W before clip: min=0.000000, max=12.771266, mean=1.000000, std=1.458209
2025-07-22 09:59:23,160 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.825176, std=0.878934
2025-07-22 09:59:23,160 - INFO: Starting regression paradigm
2025-07-22 09:59:23,160 - INFO: Training backend model 'LogNormal' on fold 7...
2025-07-22 09:59:23,160 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:59:23,475 - INFO: Fold 7 Training C-index: 0.5245
2025-07-22 09:59:23,777 - INFO: Fold 7 Test C-index: 0.5000
2025-07-22 09:59:23,778 - INFO: --- Fold 8/10 ---
2025-07-22 09:59:23,783 - INFO: Features for this fold have been standardized.
2025-07-22 09:59:23,783 - INFO: Using SRDO reweighting
2025-07-22 09:59:28,286 - INFO: SRDO reweighting completed.
2025-07-22 09:59:28,286 - INFO: Weights calculated and normalized for fold 8.
2025-07-22 09:59:28,286 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.020670
2025-07-22 09:59:28,286 - INFO: After normalization - max: 10.004100, min: 0.000000
2025-07-22 09:59:28,286 - INFO: W before clip: min=0.000000, max=10.004100, mean=1.000000, std=1.422338
2025-07-22 09:59:28,287 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.818027, std=0.871052
2025-07-22 09:59:28,287 - INFO: Starting regression paradigm
2025-07-22 09:59:28,287 - INFO: Training backend model 'LogNormal' on fold 8...
2025-07-22 09:59:28,287 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:59:28,605 - INFO: Fold 8 Training C-index: 0.5254
2025-07-22 09:59:28,903 - INFO: Fold 8 Test C-index: 0.4125
2025-07-22 09:59:28,904 - INFO: --- Fold 9/10 ---
2025-07-22 09:59:28,908 - INFO: Features for this fold have been standardized.
2025-07-22 09:59:28,909 - INFO: Using SRDO reweighting
2025-07-22 09:59:33,881 - INFO: SRDO reweighting completed.
2025-07-22 09:59:33,881 - INFO: Weights calculated and normalized for fold 9.
2025-07-22 09:59:33,881 - INFO: Weight statistics - mean: 0.002062, min: 0.000000, max: 0.050855
2025-07-22 09:59:33,881 - INFO: After normalization - max: 24.664874, min: 0.000000
2025-07-22 09:59:33,881 - INFO: W before clip: min=0.000000, max=24.664874, mean=1.000000, std=1.717540
2025-07-22 09:59:33,881 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.798687, std=0.873136
2025-07-22 09:59:33,882 - INFO: Starting regression paradigm
2025-07-22 09:59:33,882 - INFO: Training backend model 'LogNormal' on fold 9...
2025-07-22 09:59:33,882 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:59:34,095 - INFO: Fold 9 Training C-index: 0.5067
2025-07-22 09:59:34,283 - INFO: Fold 9 Test C-index: 0.4697
2025-07-22 09:59:34,284 - INFO: --- Fold 10/10 ---
2025-07-22 09:59:34,287 - INFO: Features for this fold have been standardized.
2025-07-22 09:59:34,287 - INFO: Using SRDO reweighting
2025-07-22 09:59:38,583 - INFO: SRDO reweighting completed.
2025-07-22 09:59:38,583 - INFO: Weights calculated and normalized for fold 10.
2025-07-22 09:59:38,583 - INFO: Weight statistics - mean: 0.002062, min: 0.000000, max: 0.020951
2025-07-22 09:59:38,583 - INFO: After normalization - max: 10.161128, min: 0.000000
2025-07-22 09:59:38,584 - INFO: W before clip: min=0.000000, max=10.161128, mean=1.000000, std=1.390094
2025-07-22 09:59:38,584 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.825733, std=0.890474
2025-07-22 09:59:38,584 - INFO: Starting regression paradigm
2025-07-22 09:59:38,584 - INFO: Training backend model 'LogNormal' on fold 10...
2025-07-22 09:59:38,584 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:59:38,751 - INFO: Fold 10 Training C-index: 0.5282
2025-07-22 09:59:38,967 - INFO: Fold 10 Test C-index: 0.4578
2025-07-22 09:59:38,973 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 8 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-22 09:59:38,973 - INFO: --- Starting Round 8 with Seed 10 ---
2025-07-22 09:59:38,993 - INFO: Successfully loaded data from processed_equipment_data_new.csv. Shape: (598, 187)
2025-07-22 09:59:38,994 - INFO: Succsessfully get feature columns. Total: 180
2025-07-22 09:59:38,996 - INFO: --- Fold 1/10 ---
2025-07-22 09:59:39,018 - INFO: Features for this fold have been standardized.
2025-07-22 09:59:39,020 - INFO: Using SRDO reweighting
2025-07-22 09:59:39,873 - INFO: SRDO reweighting completed.
2025-07-22 09:59:39,873 - INFO: Weights calculated and normalized for fold 1.
2025-07-22 09:59:39,873 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.013407
2025-07-22 09:59:39,874 - INFO: After normalization - max: 6.488779, min: 0.000000
2025-07-22 09:59:39,874 - INFO: W before clip: min=0.000000, max=6.488779, mean=1.000000, std=1.168286
2025-07-22 09:59:39,874 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.909279, std=0.959880
2025-07-22 09:59:39,874 - INFO: Starting regression paradigm
2025-07-22 09:59:39,874 - INFO: Training backend model 'LogNormal' on fold 1...
2025-07-22 09:59:39,874 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:59:40,538 - INFO: Fold 1 Training C-index: 0.5221
2025-07-22 09:59:40,727 - INFO: Fold 1 Test C-index: 0.3500
2025-07-22 09:59:40,727 - INFO: Fold 1: New best CV model with fold validation C-index: 0.3935
2025-07-22 09:59:40,727 - INFO: --- Fold 2/10 ---
2025-07-22 09:59:40,730 - INFO: Features for this fold have been standardized.
2025-07-22 09:59:40,730 - INFO: Using SRDO reweighting
2025-07-22 09:59:44,355 - INFO: SRDO reweighting completed.
2025-07-22 09:59:44,355 - INFO: Weights calculated and normalized for fold 2.
2025-07-22 09:59:44,355 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.022942
2025-07-22 09:59:44,355 - INFO: After normalization - max: 11.103989, min: 0.000000
2025-07-22 09:59:44,355 - INFO: W before clip: min=0.000000, max=11.103989, mean=1.000000, std=1.523764
2025-07-22 09:59:44,355 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.791545, std=0.910018
2025-07-22 09:59:44,355 - INFO: Starting regression paradigm
2025-07-22 09:59:44,355 - INFO: Training backend model 'LogNormal' on fold 2...
2025-07-22 09:59:44,355 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:59:44,528 - INFO: Fold 2 Training C-index: 0.5134
2025-07-22 09:59:44,701 - INFO: Fold 2 Test C-index: 0.3718
2025-07-22 09:59:44,702 - INFO: Fold 2: New best CV model with fold validation C-index: 0.4143
2025-07-22 09:59:44,702 - INFO: --- Fold 3/10 ---
2025-07-22 09:59:44,705 - INFO: Features for this fold have been standardized.
2025-07-22 09:59:44,705 - INFO: Using SRDO reweighting
2025-07-22 09:59:47,895 - INFO: SRDO reweighting completed.
2025-07-22 09:59:47,895 - INFO: Weights calculated and normalized for fold 3.
2025-07-22 09:59:47,895 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.037916
2025-07-22 09:59:47,896 - INFO: After normalization - max: 18.351508, min: 0.000000
2025-07-22 09:59:47,896 - INFO: W before clip: min=0.000000, max=18.351508, mean=1.000000, std=1.704788
2025-07-22 09:59:47,896 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.770161, std=0.888142
2025-07-22 09:59:47,896 - INFO: Starting regression paradigm
2025-07-22 09:59:47,896 - INFO: Training backend model 'LogNormal' on fold 3...
2025-07-22 09:59:47,896 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:59:48,147 - INFO: Fold 3 Training C-index: 0.5228
2025-07-22 09:59:48,395 - INFO: Fold 3 Test C-index: 0.4103
2025-07-22 09:59:48,395 - INFO: Fold 3: New best CV model with fold validation C-index: 0.4440
2025-07-22 09:59:48,395 - INFO: --- Fold 4/10 ---
2025-07-22 09:59:48,400 - INFO: Features for this fold have been standardized.
2025-07-22 09:59:48,400 - INFO: Using SRDO reweighting
2025-07-22 09:59:52,561 - INFO: SRDO reweighting completed.
2025-07-22 09:59:52,561 - INFO: Weights calculated and normalized for fold 4.
2025-07-22 09:59:52,561 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.021538
2025-07-22 09:59:52,562 - INFO: After normalization - max: 10.424408, min: 0.000000
2025-07-22 09:59:52,562 - INFO: W before clip: min=0.000000, max=10.424408, mean=1.000000, std=1.471090
2025-07-22 09:59:52,562 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.796059, std=0.901764
2025-07-22 09:59:52,562 - INFO: Starting regression paradigm
2025-07-22 09:59:52,562 - INFO: Training backend model 'LogNormal' on fold 4...
2025-07-22 09:59:52,562 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:59:52,729 - INFO: Fold 4 Training C-index: 0.5065
2025-07-22 09:59:52,903 - INFO: Fold 4 Test C-index: 0.4286
2025-07-22 09:59:52,903 - INFO: Fold 4: New best CV model with fold validation C-index: 0.4519
2025-07-22 09:59:52,903 - INFO: --- Fold 5/10 ---
2025-07-22 09:59:52,906 - INFO: Features for this fold have been standardized.
2025-07-22 09:59:52,907 - INFO: Using SRDO reweighting
2025-07-22 09:59:58,056 - INFO: SRDO reweighting completed.
2025-07-22 09:59:58,056 - INFO: Weights calculated and normalized for fold 5.
2025-07-22 09:59:58,056 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.019406
2025-07-22 09:59:58,056 - INFO: After normalization - max: 9.392588, min: 0.000000
2025-07-22 09:59:58,057 - INFO: W before clip: min=0.000000, max=9.392588, mean=1.000000, std=1.423066
2025-07-22 09:59:58,057 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.809912, std=0.897931
2025-07-22 09:59:58,057 - INFO: Starting regression paradigm
2025-07-22 09:59:58,057 - INFO: Training backend model 'LogNormal' on fold 5...
2025-07-22 09:59:58,057 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 09:59:58,228 - INFO: Fold 5 Training C-index: 0.5076
2025-07-22 09:59:58,405 - INFO: Fold 5 Test C-index: 0.4713
2025-07-22 09:59:58,405 - INFO: Fold 5: New best CV model with fold validation C-index: 0.4822
2025-07-22 09:59:58,405 - INFO: --- Fold 6/10 ---
2025-07-22 09:59:58,408 - INFO: Features for this fold have been standardized.
2025-07-22 09:59:58,409 - INFO: Using SRDO reweighting
2025-07-22 10:00:01,679 - INFO: SRDO reweighting completed.
2025-07-22 10:00:01,679 - INFO: Weights calculated and normalized for fold 6.
2025-07-22 10:00:01,679 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.024537
2025-07-22 10:00:01,679 - INFO: After normalization - max: 11.875958, min: 0.000000
2025-07-22 10:00:01,679 - INFO: W before clip: min=0.000000, max=11.875958, mean=1.000000, std=1.526738
2025-07-22 10:00:01,679 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.798625, std=0.865464
2025-07-22 10:00:01,679 - INFO: Starting regression paradigm
2025-07-22 10:00:01,679 - INFO: Training backend model 'LogNormal' on fold 6...
2025-07-22 10:00:01,679 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 10:00:01,844 - INFO: Fold 6 Training C-index: 0.4953

Number of selected features: 10
Selected features 9, top 5: ['Z_CLOSED_LOOP_V280_L_Y', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y', 'PULSE_280V_X1_HIGH', 'EPI_280V_X1_LOW']
Number of selected features: 10
Selected features 9, top 5: ['Z_CLOSED_LOOP_V280_L_Y', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y', 'PULSE_280V_X1_HIGH', 'EPI_700V_Y2_LOW']
Number of selected features: 10
Selected features 9, top 5: ['Z_CLOSED_LOOP_V280_L_Y', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y', 'EPI_700V_Y2_LOW', 'EPI_280V_X1_LOW']
Number of selected features: 10
Selected features 9, top 5: ['Z_CLOSED_LOOP_V280_L_Y', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y', 'EPI_700V_Y2_LOW', 'EPI_280V_X1_LOW']
Number of selected features: 10
Selected features 9, top 5: ['Z_CLOSED_LOOP_V280_L_Y', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y', 'PULSE_280V_X1_HIGH', 'EPI_280V_X1_LOW']
Number of selected features: 10
Selected features 9, top 5: ['Z_CLOSED_LOOP_V280_L_Y', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y', 'EPI_700V_Y2_LOW', 'EPI_280V_X1_LOW']
Number of selected features: 10
Selected features 9, top 5: ['Z_CLOSED_LOOP_V280_L_Y', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y', 'EPI_700V_Y2_LOW', 'EPI_280V_X1_LOW']
Number of selected features: 10
Selected features 9, top 5: ['Y_CLOSED_LOOP_V280_H_X', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'EPI_700V_Z2_HIGH', '300V_V700_H_Z', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y']
Number of selected features: 10
Selected features 9, top 5: ['Y_CLOSED_LOOP_V280_H_X', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'EPI_700V_Z2_HIGH', '300V_V700_H_Z', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y']
Number of selected features: 10
Selected features 9, top 5: ['Y_CLOSED_LOOP_V280_H_X', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'EPI_700V_Z2_HIGH', '300V_V700_H_Z', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y']
Number of selected features: 10
Selected features 9, top 5: ['Y_CLOSED_LOOP_V280_H_X', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'EPI_700V_Z2_HIGH', '300V_V700_H_Z', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y']
Number of selected features: 10
Selected features 9, top 5: ['Y_CLOSED_LOOP_V280_H_X', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'EPI_700V_Z2_HIGH', '300V_V700_H_Z', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y']
Number of selected features: 10
Selected features 9, top 5: ['Y_CLOSED_LOOP_V280_H_X', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'EPI_700V_Z2_HIGH', '300V_V700_H_Z', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y']
Number of selected features: 10
Selected features 9, top 5: ['Y_CLOSED_LOOP_V280_H_X', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'EPI_700V_Z2_HIGH', '300V_V700_H_Z', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y']
Number of selected features: 10
Selected features 9, top 5: ['Y_CLOSED_LOOP_V280_H_X', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'EPI_700V_Z2_HIGH', '300V_V700_H_Z', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y']
Number of selected features: 10
Selected features 9, top 5: ['Y_CLOSED_LOOP_V280_H_X', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'EPI_700V_Z2_HIGH', '300V_V700_H_Z', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y']
Number of selected features: 10
Selected features 9, top 5: ['Y_CLOSED_LOOP_V280_H_X', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'EPI_700V_Z2_HIGH', '300V_V700_H_Z', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y']
Number of selected features: 10
Selected features 9, top 5: ['EPI_700V_X2_LOW', '300V_V700_L_X', 'Z_CLOSED_LOOP_V280_L_Y', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X']
Number of selected features: 10
Selected features 9, top 5: ['EPI_700V_X2_LOW', '300V_V700_L_X', 'Z_CLOSED_LOOP_V280_L_Y', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X']
Number of selected features: 10
Selected features 9, top 5: ['EPI_700V_X2_LOW', '300V_V700_L_X', 'Z_CLOSED_LOOP_V280_L_Y', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X']
Number of selected features: 10
Selected features 9, top 5: ['EPI_700V_X2_LOW', '300V_V700_L_X', 'Z_CLOSED_LOOP_V280_L_Y', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X']
Number of selected features: 10
Selected features 9, top 5: ['EPI_700V_X2_LOW', '300V_V700_L_X', 'Z_CLOSED_LOOP_V280_L_Y', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X']
Number of selected features: 10
Selected features 9, top 5: ['EPI_700V_X2_LOW', '300V_V700_L_X', 'Z_CLOSED_LOOP_V280_L_Y', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X']
Number of selected features: 10
Selected features 9, top 5: ['EPI_700V_X2_LOW', '300V_V700_L_X', 'Z_CLOSED_LOOP_V280_L_Y', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X']
Number of selected features: 10
Selected features 9, top 5: ['EPI_700V_X2_LOW', '300V_V700_L_X', 'Z_CLOSED_LOOP_V280_L_Y', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X']
Number of selected features: 10
Selected features 9, top 5: ['EPI_700V_X2_LOW', '300V_V700_L_X', 'Z_CLOSED_LOOP_V280_L_Y', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X']
Number of selected features: 10
Selected features 9, top 5: ['EPI_700V_X2_LOW', '300V_V700_L_X', 'Z_CLOSED_LOOP_V280_L_Y', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X']
Number of selected features: 10
Selected features 9, top 5: ['Z_CLOSED_LOOP_V280_L_Y', 'Y_CLOSED_LOOP_V280_L_X', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y']
Number of selected features: 10
Selected features 9, top 5: ['Y_CLOSED_LOOP_V280_L_X', 'Z_CLOSED_LOOP_V280_L_Y', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y']
Number of selected features: 10
Selected features 9, top 5: ['Z_CLOSED_LOOP_V280_L_Y', 'Y_CLOSED_LOOP_V280_L_X', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y']
Number of selected features: 10
Selected features 9, top 5: ['Z_CLOSED_LOOP_V280_L_Y', 'Y_CLOSED_LOOP_V280_L_X', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y']
Number of selected features: 10
Selected features 9, top 5: ['Y_CLOSED_LOOP_V280_L_X', 'Z_CLOSED_LOOP_V280_L_Y', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y']
Number of selected features: 10
Selected features 9, top 5: ['Z_CLOSED_LOOP_V280_L_Y', 'Y_CLOSED_LOOP_V280_L_X', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y']
Number of selected features: 10
Selected features 9, top 5: ['Z_CLOSED_LOOP_V280_L_Y', 'Y_CLOSED_LOOP_V280_L_X', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y']
Number of selected features: 10
Selected features 9, top 5: ['Z_CLOSED_LOOP_V280_L_Y', 'Y_CLOSED_LOOP_V280_L_X', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y']
Number of selected features: 10
Selected features 9, top 5: ['Z_CLOSED_LOOP_V280_L_Y', 'Y_CLOSED_LOOP_V280_L_X', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y']
Number of selected features: 10
Selected features 9, top 5: ['Z_CLOSED_LOOP_V280_L_Y', 'Y_CLOSED_LOOP_V280_L_X', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y']
Number of selected features: 10
Selected features 9, top 5: ['EPI_700V_X2_LOW', 'OUTPUT_VOLTAGE_DISCH_V700_L_X', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'EPI_700V_Z2_HIGH']
Number of selected features: 10
Selected features 9, top 5: ['EPI_700V_X2_LOW', 'OUTPUT_VOLTAGE_DISCH_V700_L_X', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'EPI_700V_Z2_HIGH']
Number of selected features: 10
Selected features 9, top 5: ['EPI_700V_X2_LOW', 'OUTPUT_VOLTAGE_DISCH_V700_L_X', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'EPI_700V_Z2_HIGH']
Number of selected features: 10
Selected features 9, top 5: ['EPI_700V_X2_LOW', 'OUTPUT_VOLTAGE_DISCH_V700_L_X', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'EPI_700V_Z2_HIGH']
Number of selected features: 10
Selected features 9, top 5: ['EPI_700V_X2_LOW', 'OUTPUT_VOLTAGE_DISCH_V700_L_X', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'EPI_700V_Z2_HIGH']
Number of selected features: 10
Selected features 9, top 5: ['EPI_700V_X2_LOW', 'OUTPUT_VOLTAGE_DISCH_V700_L_X', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'EPI_700V_Z2_HIGH']2025-07-22 10:00:02,048 - INFO: Fold 6 Test C-index: 0.5488
2025-07-22 10:00:02,048 - INFO: Fold 6: New best CV model with fold validation C-index: 0.5327
2025-07-22 10:00:02,049 - INFO: --- Fold 7/10 ---
2025-07-22 10:00:02,053 - INFO: Features for this fold have been standardized.
2025-07-22 10:00:02,053 - INFO: Using SRDO reweighting
2025-07-22 10:00:07,002 - INFO: SRDO reweighting completed.
2025-07-22 10:00:07,002 - INFO: Weights calculated and normalized for fold 7.
2025-07-22 10:00:07,002 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.031419
2025-07-22 10:00:07,002 - INFO: After normalization - max: 15.206609, min: 0.000000
2025-07-22 10:00:07,002 - INFO: W before clip: min=0.000000, max=15.206609, mean=1.000000, std=1.615322
2025-07-22 10:00:07,002 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.787649, std=0.903726
2025-07-22 10:00:07,002 - INFO: Starting regression paradigm
2025-07-22 10:00:07,002 - INFO: Training backend model 'LogNormal' on fold 7...
2025-07-22 10:00:07,002 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 10:00:07,163 - INFO: Fold 7 Training C-index: 0.5350
2025-07-22 10:00:07,360 - INFO: Fold 7 Test C-index: 0.2949
2025-07-22 10:00:07,360 - INFO: --- Fold 8/10 ---
2025-07-22 10:00:07,364 - INFO: Features for this fold have been standardized.
2025-07-22 10:00:07,364 - INFO: Using SRDO reweighting
2025-07-22 10:00:12,198 - INFO: SRDO reweighting completed.
2025-07-22 10:00:12,199 - INFO: Weights calculated and normalized for fold 8.
2025-07-22 10:00:12,199 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.029585
2025-07-22 10:00:12,199 - INFO: After normalization - max: 14.319213, min: 0.000000
2025-07-22 10:00:12,199 - INFO: W before clip: min=0.000000, max=14.319213, mean=1.000000, std=1.545309
2025-07-22 10:00:12,199 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.805322, std=0.883967
2025-07-22 10:00:12,199 - INFO: Starting regression paradigm
2025-07-22 10:00:12,200 - INFO: Training backend model 'LogNormal' on fold 8...
2025-07-22 10:00:12,200 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 10:00:12,434 - INFO: Fold 8 Training C-index: 0.4904
2025-07-22 10:00:12,681 - INFO: Fold 8 Test C-index: 0.5833
2025-07-22 10:00:12,681 - INFO: Fold 8: New best CV model with fold validation C-index: 0.5555
2025-07-22 10:00:12,681 - INFO: --- Fold 9/10 ---
2025-07-22 10:00:12,685 - INFO: Features for this fold have been standardized.
2025-07-22 10:00:12,686 - INFO: Using SRDO reweighting
2025-07-22 10:00:17,706 - INFO: SRDO reweighting completed.
2025-07-22 10:00:17,706 - INFO: Weights calculated and normalized for fold 9.
2025-07-22 10:00:17,706 - INFO: Weight statistics - mean: 0.002062, min: 0.000000, max: 0.024453
2025-07-22 10:00:17,706 - INFO: After normalization - max: 11.859737, min: 0.000000
2025-07-22 10:00:17,706 - INFO: W before clip: min=0.000000, max=11.859737, mean=1.000000, std=1.472416
2025-07-22 10:00:17,707 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.806983, std=0.903852
2025-07-22 10:00:17,707 - INFO: Starting regression paradigm
2025-07-22 10:00:17,707 - INFO: Training backend model 'LogNormal' on fold 9...
2025-07-22 10:00:17,707 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 10:00:17,971 - INFO: Fold 9 Training C-index: 0.4975
2025-07-22 10:00:18,217 - INFO: Fold 9 Test C-index: 0.6316
2025-07-22 10:00:18,217 - INFO: Fold 9: New best CV model with fold validation C-index: 0.5914
2025-07-22 10:00:18,217 - INFO: --- Fold 10/10 ---
2025-07-22 10:00:18,221 - INFO: Features for this fold have been standardized.
2025-07-22 10:00:18,222 - INFO: Using SRDO reweighting
2025-07-22 10:00:21,773 - INFO: SRDO reweighting completed.
2025-07-22 10:00:21,773 - INFO: Weights calculated and normalized for fold 10.
2025-07-22 10:00:21,773 - INFO: Weight statistics - mean: 0.002062, min: 0.000000, max: 0.018690
2025-07-22 10:00:21,773 - INFO: After normalization - max: 9.064849, min: 0.000000
2025-07-22 10:00:21,774 - INFO: W before clip: min=0.000000, max=9.064849, mean=1.000000, std=1.435903
2025-07-22 10:00:21,774 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.819820, std=0.917140
2025-07-22 10:00:21,774 - INFO: Starting regression paradigm
2025-07-22 10:00:21,774 - INFO: Training backend model 'LogNormal' on fold 10...
2025-07-22 10:00:21,774 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 10:00:21,945 - INFO: Fold 10 Training C-index: 0.4844
2025-07-22 10:00:22,127 - INFO: Fold 10 Test C-index: 0.7027
2025-07-22 10:00:22,127 - INFO: Fold 10: New best CV model with fold validation C-index: 0.6372
2025-07-22 10:00:22,132 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 9 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-22 10:00:22,132 - INFO: --- Starting Round 9 with Seed 11 ---
2025-07-22 10:00:22,146 - INFO: Successfully loaded data from processed_equipment_data_new.csv. Shape: (598, 187)
2025-07-22 10:00:22,147 - INFO: Succsessfully get feature columns. Total: 180
2025-07-22 10:00:22,149 - INFO: --- Fold 1/10 ---
2025-07-22 10:00:22,164 - INFO: Features for this fold have been standardized.
2025-07-22 10:00:22,166 - INFO: Using SRDO reweighting
2025-07-22 10:00:22,737 - INFO: SRDO reweighting completed.
2025-07-22 10:00:22,737 - INFO: Weights calculated and normalized for fold 1.
2025-07-22 10:00:22,737 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.012039
2025-07-22 10:00:22,737 - INFO: After normalization - max: 5.827090, min: 0.000000
2025-07-22 10:00:22,737 - INFO: W before clip: min=0.000000, max=5.827090, mean=1.000000, std=1.198168
2025-07-22 10:00:22,737 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.886923, std=0.935350
2025-07-22 10:00:22,737 - INFO: Starting regression paradigm
2025-07-22 10:00:22,737 - INFO: Training backend model 'LogNormal' on fold 1...
2025-07-22 10:00:22,737 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 10:00:23,416 - INFO: Fold 1 Training C-index: 0.5102
2025-07-22 10:00:23,605 - INFO: Fold 1 Test C-index: 0.5875
2025-07-22 10:00:23,605 - INFO: Fold 1: New best CV model with fold validation C-index: 0.5655
2025-07-22 10:00:23,605 - INFO: --- Fold 2/10 ---
2025-07-22 10:00:23,608 - INFO: Features for this fold have been standardized.
2025-07-22 10:00:23,609 - INFO: Using SRDO reweighting
2025-07-22 10:00:27,623 - INFO: SRDO reweighting completed.
2025-07-22 10:00:27,623 - INFO: Weights calculated and normalized for fold 2.
2025-07-22 10:00:27,624 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.024496
2025-07-22 10:00:27,624 - INFO: After normalization - max: 11.856248, min: 0.000000
2025-07-22 10:00:27,624 - INFO: W before clip: min=0.000000, max=11.856248, mean=1.000000, std=1.465561
2025-07-22 10:00:27,624 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.817996, std=0.905764
2025-07-22 10:00:27,624 - INFO: Starting regression paradigm
2025-07-22 10:00:27,624 - INFO: Training backend model 'LogNormal' on fold 2...
2025-07-22 10:00:27,624 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 10:00:27,803 - INFO: Fold 2 Training C-index: 0.5027
2025-07-22 10:00:27,973 - INFO: Fold 2 Test C-index: 0.5795
2025-07-22 10:00:27,973 - INFO: --- Fold 3/10 ---
2025-07-22 10:00:27,976 - INFO: Features for this fold have been standardized.
2025-07-22 10:00:27,977 - INFO: Using SRDO reweighting
2025-07-22 10:00:31,503 - INFO: SRDO reweighting completed.
2025-07-22 10:00:31,504 - INFO: Weights calculated and normalized for fold 3.
2025-07-22 10:00:31,504 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.020726
2025-07-22 10:00:31,504 - INFO: After normalization - max: 10.031359, min: 0.000000
2025-07-22 10:00:31,504 - INFO: W before clip: min=0.000000, max=10.031359, mean=1.000000, std=1.476869
2025-07-22 10:00:31,504 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.813478, std=0.893004
2025-07-22 10:00:31,504 - INFO: Starting regression paradigm
2025-07-22 10:00:31,504 - INFO: Training backend model 'LogNormal' on fold 3...
2025-07-22 10:00:31,505 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 10:00:31,744 - INFO: Fold 3 Training C-index: 0.5044
2025-07-22 10:00:32,004 - INFO: Fold 3 Test C-index: 0.4588
2025-07-22 10:00:32,004 - INFO: --- Fold 4/10 ---
2025-07-22 10:00:32,008 - INFO: Features for this fold have been standardized.
2025-07-22 10:00:32,009 - INFO: Using SRDO reweighting
2025-07-22 10:00:35,471 - INFO: SRDO reweighting completed.
2025-07-22 10:00:35,472 - INFO: Weights calculated and normalized for fold 4.
2025-07-22 10:00:35,472 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.019995
2025-07-22 10:00:35,472 - INFO: After normalization - max: 9.677501, min: 0.000000
2025-07-22 10:00:35,472 - INFO: W before clip: min=0.000000, max=9.677501, mean=1.000000, std=1.347788
2025-07-22 10:00:35,472 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.843315, std=0.920749
2025-07-22 10:00:35,472 - INFO: Starting regression paradigm
2025-07-22 10:00:35,472 - INFO: Training backend model 'LogNormal' on fold 4...
2025-07-22 10:00:35,472 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 10:00:35,637 - INFO: Fold 4 Training C-index: 0.5103
2025-07-22 10:00:35,805 - INFO: Fold 4 Test C-index: 0.5000
2025-07-22 10:00:35,805 - INFO: --- Fold 5/10 ---
2025-07-22 10:00:35,808 - INFO: Features for this fold have been standardized.
2025-07-22 10:00:35,808 - INFO: Using SRDO reweighting
2025-07-22 10:00:39,432 - INFO: SRDO reweighting completed.
2025-07-22 10:00:39,432 - INFO: Weights calculated and normalized for fold 5.
2025-07-22 10:00:39,432 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.016820
2025-07-22 10:00:39,432 - INFO: After normalization - max: 8.140830, min: 0.000000
2025-07-22 10:00:39,432 - INFO: W before clip: min=0.000000, max=8.140830, mean=1.000000, std=1.353440
2025-07-22 10:00:39,432 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.833123, std=0.916907
2025-07-22 10:00:39,432 - INFO: Starting regression paradigm
2025-07-22 10:00:39,432 - INFO: Training backend model 'LogNormal' on fold 5...
2025-07-22 10:00:39,432 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 10:00:39,595 - INFO: Fold 5 Training C-index: 0.5196
2025-07-22 10:00:39,764 - INFO: Fold 5 Test C-index: 0.3780
2025-07-22 10:00:39,764 - INFO: --- Fold 6/10 ---
2025-07-22 10:00:39,767 - INFO: Features for this fold have been standardized.
2025-07-22 10:00:39,768 - INFO: Using SRDO reweighting
2025-07-22 10:00:42,606 - INFO: SRDO reweighting completed.
2025-07-22 10:00:42,606 - INFO: Weights calculated and normalized for fold 6.
2025-07-22 10:00:42,607 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.019434
2025-07-22 10:00:42,607 - INFO: After normalization - max: 9.406139, min: 0.000000
2025-07-22 10:00:42,607 - INFO: W before clip: min=0.000000, max=9.406139, mean=1.000000, std=1.417359
2025-07-22 10:00:42,607 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.818165, std=0.905016
2025-07-22 10:00:42,607 - INFO: Starting regression paradigm
2025-07-22 10:00:42,607 - INFO: Training backend model 'LogNormal' on fold 6...
2025-07-22 10:00:42,607 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 10:00:42,879 - INFO: Fold 6 Training C-index: 0.5163
2025-07-22 10:00:43,124 - INFO: Fold 6 Test C-index: 0.5185
2025-07-22 10:00:43,124 - INFO: --- Fold 7/10 ---
2025-07-22 10:00:43,129 - INFO: Features for this fold have been standardized.
2025-07-22 10:00:43,129 - INFO: Using SRDO reweighting
2025-07-22 10:00:46,671 - INFO: SRDO reweighting completed.
2025-07-22 10:00:46,671 - INFO: Weights calculated and normalized for fold 7.
2025-07-22 10:00:46,671 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.020880
2025-07-22 10:00:46,671 - INFO: After normalization - max: 10.105842, min: 0.000000
2025-07-22 10:00:46,671 - INFO: W before clip: min=0.000000, max=10.105842, mean=1.000000, std=1.370973
2025-07-22 10:00:46,671 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.836255, std=0.898954
2025-07-22 10:00:46,671 - INFO: Starting regression paradigm
2025-07-22 10:00:46,671 - INFO: Training backend model 'LogNormal' on fold 7...
2025-07-22 10:00:46,671 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 10:00:46,833 - INFO: Fold 7 Training C-index: 0.5137
2025-07-22 10:00:47,002 - INFO: Fold 7 Test C-index: 0.5455
2025-07-22 10:00:47,002 - INFO: --- Fold 8/10 ---
2025-07-22 10:00:47,005 - INFO: Features for this fold have been standardized.
2025-07-22 10:00:47,005 - INFO: Using SRDO reweighting
2025-07-22 10:00:51,696 - INFO: SRDO reweighting completed.
2025-07-22 10:00:51,697 - INFO: Weights calculated and normalized for fold 8.
2025-07-22 10:00:51,697 - INFO: Weight statistics - mean: 0.002066, min: 0.000000, max: 0.025679
2025-07-22 10:00:51,697 - INFO: After normalization - max: 12.428742, min: 0.000000
2025-07-22 10:00:51,697 - INFO: W before clip: min=0.000000, max=12.428742, mean=1.000000, std=1.503009
2025-07-22 10:00:51,697 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.801321, std=0.917911
2025-07-22 10:00:51,697 - INFO: Starting regression paradigm
2025-07-22 10:00:51,697 - INFO: Training backend model 'LogNormal' on fold 8...
2025-07-22 10:00:51,697 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 10:00:51,858 - INFO: Fold 8 Training C-index: 0.5104
2025-07-22 10:00:52,032 - INFO: Fold 8 Test C-index: 0.5119
2025-07-22 10:00:52,032 - INFO: --- Fold 9/10 ---
2025-07-22 10:00:52,035 - INFO: Features for this fold have been standardized.
2025-07-22 10:00:52,036 - INFO: Using SRDO reweighting
2025-07-22 10:00:56,195 - INFO: SRDO reweighting completed.
2025-07-22 10:00:56,195 - INFO: Weights calculated and normalized for fold 9.
2025-07-22 10:00:56,195 - INFO: Weight statistics - mean: 0.002062, min: 0.000000, max: 0.020288
2025-07-22 10:00:56,195 - INFO: After normalization - max: 9.839606, min: 0.000000
2025-07-22 10:00:56,196 - INFO: W before clip: min=0.000000, max=9.839606, mean=1.000000, std=1.430254
2025-07-22 10:00:56,196 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.812039, std=0.914830
2025-07-22 10:00:56,196 - INFO: Starting regression paradigm
2025-07-22 10:00:56,196 - INFO: Training backend model 'LogNormal' on fold 9...
2025-07-22 10:00:56,196 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 10:00:56,552 - INFO: Fold 9 Training C-index: 0.5306
2025-07-22 10:00:56,889 - INFO: Fold 9 Test C-index: 0.2235
2025-07-22 10:00:56,889 - INFO: --- Fold 10/10 ---
2025-07-22 10:00:56,896 - INFO: Features for this fold have been standardized.
2025-07-22 10:00:56,897 - INFO: Using SRDO reweighting
2025-07-22 10:01:01,369 - INFO: SRDO reweighting completed.
2025-07-22 10:01:01,369 - INFO: Weights calculated and normalized for fold 10.
2025-07-22 10:01:01,369 - INFO: Weight statistics - mean: 0.002062, min: 0.000000, max: 0.018804
2025-07-22 10:01:01,369 - INFO: After normalization - max: 9.120112, min: 0.000000
2025-07-22 10:01:01,369 - INFO: W before clip: min=0.000000, max=9.120112, mean=1.000000, std=1.472569
2025-07-22 10:01:01,369 - INFO: W after clip: min=0.004483, max=2.600000, mean=0.785491, std=0.891238
2025-07-22 10:01:01,369 - INFO: Starting regression paradigm
2025-07-22 10:01:01,369 - INFO: Training backend model 'LogNormal' on fold 10...
2025-07-22 10:01:01,369 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-22 10:01:01,682 - INFO: Fold 10 Training C-index: 0.5016
2025-07-22 10:01:01,981 - INFO: Fold 10 Test C-index: 0.5758
2025-07-22 10:01:01,988 - INFO: ==================================================
2025-07-22 10:01:01,989 - INFO: Final Summary Over 10 Independent Run(s), validation set (hold-out set)
2025-07-22 10:01:01,989 - INFO: ==================================================
2025-07-22 10:01:01,989 - INFO: Average of 'mean_cv_fold_val_c_index' over 10 runs: 0.5047 (Std Dev: 0.0168)
2025-07-22 10:01:01,989 - INFO: Average of 'std_cv_fold_val_c_index' over 10 runs: 0.0948 (Std Dev: 0.0179)
2025-07-22 10:01:01,989 - INFO: Average of 'worst_cv_fold_val_c_index' over 10 runs: 0.3401 (Std Dev: 0.0595)
2025-07-22 10:01:01,989 - INFO: Average of 'final_test_c_index' over 10 runs: 0.4952 (Std Dev: 0.0732)
2025-07-22 10:01:01,989 - INFO: ==================================================
2025-07-22 10:01:01,989 - INFO: Final Summary Over 10 Independent Run(s), test set (in cross-validation)
2025-07-22 10:01:01,989 - INFO: ==================================================
2025-07-22 10:01:01,989 - INFO: ==================================================
2025-07-22 10:01:01,989 - INFO: Final Test Set Performance Summary:
2025-07-22 10:01:01,989 - INFO: Final Test C-indices: ['0.4149', '0.5319', '0.5165', '0.3967', '0.4792', '0.3750', '0.6162', '0.5510', '0.5340', '0.5368']
2025-07-22 10:01:01,989 - INFO: Mean Final Test C-index: 0.4952
2025-07-22 10:01:01,989 - INFO: Best Final Test C-index: 0.6162
2025-07-22 10:01:01,989 - INFO: Std Dev of Final Test C-index: 0.0732
2025-07-22 10:01:01,989 - INFO: Worst Final Test C-index: 0.3750
2025-07-22 10:01:01,989 - INFO: ==================================================

Number of selected features: 10
Selected features 9, top 5: ['EPI_700V_X2_LOW', 'OUTPUT_VOLTAGE_DISCH_V700_L_X', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'EPI_700V_Z2_HIGH']
Number of selected features: 10
Selected features 9, top 5: ['EPI_700V_X2_LOW', 'OUTPUT_VOLTAGE_DISCH_V700_L_X', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'EPI_700V_Z2_HIGH']
Number of selected features: 10
Selected features 9, top 5: ['EPI_700V_X2_LOW', 'OUTPUT_VOLTAGE_DISCH_V700_L_X', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'EPI_700V_Z2_HIGH']
Number of selected features: 10
Selected features 9, top 5: ['EPI_700V_X2_LOW', 'OUTPUT_VOLTAGE_DISCH_V700_L_X', 'PULSE_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'EPI_700V_Z2_HIGH']
Number of selected features: 10
Selected features 9, top 5: ['EPI_700V_Y2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'EPI_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y', 'EPI_700V_Y2_LOW']
Number of selected features: 10
Selected features 9, top 5: ['EPI_700V_Y2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'EPI_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y', 'EPI_700V_Y2_LOW']
Number of selected features: 10
Selected features 9, top 5: ['EPI_700V_Y2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'EPI_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y', 'EPI_700V_Y2_LOW']
Number of selected features: 10
Selected features 9, top 5: ['EPI_700V_Y2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'EPI_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y', 'EPI_700V_Y2_LOW']
Number of selected features: 10
Selected features 9, top 5: ['EPI_700V_Y2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'EPI_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y', 'EPI_700V_Y2_LOW']
Number of selected features: 10
Selected features 9, top 5: ['EPI_700V_Y2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'EPI_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y', 'EPI_700V_Y2_LOW']
Number of selected features: 10
Selected features 9, top 5: ['EPI_700V_Y2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'EPI_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y', 'EPI_700V_Y2_LOW']
Number of selected features: 10
Selected features 9, top 5: ['EPI_700V_Y2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'EPI_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y', 'EPI_700V_Y2_LOW']
Number of selected features: 10
Selected features 9, top 5: ['EPI_700V_Y2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'EPI_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y', 'EPI_700V_Y2_LOW']
Number of selected features: 10
Selected features 9, top 5: ['EPI_700V_Y2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_L_X', 'EPI_700V_Z2_HIGH', 'OUTPUT_VOLTAGE_DISCH_V280_H_Y', 'EPI_700V_Y2_LOW']
Total time: 601.37 seconds
