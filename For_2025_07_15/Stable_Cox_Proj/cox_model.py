"""
加权 Cox 比例风险模型实现
基于 lifelines 库，支持样本权重和特征选择
"""

import numpy as np
import pandas as pd
from lifelines import CoxPHFitter
from lifelines.statistics import logrank_test
from sklearn.feature_selection import SelectKBest, f_regression
from sklearn.preprocessing import StandardScaler
import logging

logger = logging.getLogger(__name__)

class WeightedCoxModel:
    """加权 Cox 比例风险模型"""

    def __init__(self, penalizer=0.01, l1_ratio=0.0, fit_intercept=False):
        """
        初始化加权 Cox 模型

        参数:
        - penalizer: 正则化参数
        - l1_ratio: L1 正则化比例 (0=Ridge, 1=Lasso)
        - fit_intercept: 是否拟合截距
        """
        self.penalizer = penalizer
        self.l1_ratio = l1_ratio
        self.fit_intercept = fit_intercept
        self.model_ = None
        self.feature_names_ = None
        self.scaler_ = None

    def fit(self, X, duration_col='survival_time', event_col='event',
            weights=None, feature_names=None, standardize=True):
        """
        拟合加权 Cox 模型

        参数:
        - X: 包含特征、生存时间和事件指示器的数据框
        - duration_col: 生存时间列名
        - event_col: 事件指示器列名
        - weights: 样本权重
        - feature_names: 特征名称列表
        - standardize: 是否标准化特征

        返回:
        - self
        """
        logger.info("开始拟合加权 Cox 模型")

        # 确保输入是 DataFrame
        if not isinstance(X, pd.DataFrame):
            X = pd.DataFrame(X)

        # 获取特征列
        if feature_names is None:
            feature_cols = [col for col in X.columns
                           if col not in [duration_col, event_col, 'SN_Common', 'Date']]
        else:
            feature_cols = feature_names

        # 检查是否有特征
        if len(feature_cols) == 0:
            raise ValueError("没有可用的特征列进行建模")

        self.feature_names_ = feature_cols
        logger.info(f"使用 {len(feature_cols)} 个特征进行建模")

        # 准备数据
        data = X.copy()

        # 标准化特征
        if standardize and len(feature_cols) > 0:
            self.scaler_ = StandardScaler()
            data[feature_cols] = self.scaler_.fit_transform(data[feature_cols])

        # 添加权重列
        if weights is not None:
            weights = np.array(weights).flatten()
            data['weights'] = weights
            weights_col = 'weights'
        else:
            weights_col = None

        # 创建 Cox 模型
        self.model_ = CoxPHFitter(
            penalizer=self.penalizer,
            l1_ratio=self.l1_ratio
        )

        # 拟合模型
        try:
            self.model_.fit(
                data,
                duration_col=duration_col,
                event_col=event_col,
                weights_col=weights_col
            )

            logger.info(f"Cox 模型拟合完成，特征数: {len(feature_cols)}")
            logger.info(f"模型 C-index: {self.model_.concordance_index_:.4f}")

        except Exception as e:
            logger.error(f"Cox 模型拟合失败: {e}")
            raise

        return self

    def predict(self, X, return_type='risk_score'):
        """
        预测风险评分或生存函数

        参数:
        - X: 特征数据
        - return_type: 返回类型 ('risk_score', 'survival_function', 'hazard')

        返回:
        - 预测结果
        """
        if self.model_ is None:
            raise ValueError("模型尚未拟合")

        # 确保输入是 DataFrame
        if not isinstance(X, pd.DataFrame):
            X = pd.DataFrame(X, columns=self.feature_names_)

        # 选择特征列
        X_features = X[self.feature_names_]

        # 标准化特征
        if self.scaler_ is not None:
            X_features = pd.DataFrame(
                self.scaler_.transform(X_features),
                columns=self.feature_names_,
                index=X_features.index
            )

        if return_type == 'risk_score':
            return self.model_.predict_partial_hazard(X_features)
        elif return_type == 'survival_function':
            return self.model_.predict_survival_function(X_features)
        elif return_type == 'hazard':
            return self.model_.predict_cumulative_hazard(X_features)
        else:
            raise ValueError(f"Unknown return_type: {return_type}")

    def score(self, X, duration_col='survival_time', event_col='event'):
        """
        计算模型的 C-index 评分

        参数:
        - X: 测试数据
        - duration_col: 生存时间列名
        - event_col: 事件指示器列名

        返回:
        - C-index 评分
        """
        if self.model_ is None:
            raise ValueError("模型尚未拟合")

        # 确保输入是 DataFrame
        if not isinstance(X, pd.DataFrame):
            X = pd.DataFrame(X)

        # 选择特征列
        X_features = X[self.feature_names_].copy()

        # 标准化特征
        if self.scaler_ is not None:
            X_features[self.feature_names_] = self.scaler_.transform(X_features)

        # 添加生存时间和事件列
        X_features[duration_col] = X[duration_col]
        X_features[event_col] = X[event_col]

        return self.model_.score(X_features, scoring_method='concordance_index')

    @property
    def summary(self):
        """获取模型摘要"""
        if self.model_ is None:
            raise ValueError("模型尚未拟合")
        return self.model_.summary

    @property
    def params_(self):
        """获取模型参数"""
        if self.model_ is None:
            raise ValueError("模型尚未拟合")
        return self.model_.params_

    @property
    def concordance_index_(self):
        """获取 C-index"""
        if self.model_ is None:
            raise ValueError("模型尚未拟合")
        return self.model_.concordance_index_


class CoxFeatureSelector:
    """Cox 模型特征选择器"""

    def __init__(self, method='cox_pvalue', top_n=20, p_threshold=0.05):
        """
        初始化特征选择器

        参数:
        - method: 特征选择方法 ('cox_pvalue', 'univariate', 'lasso')
        - top_n: 选择的特征数量
        - p_threshold: p 值阈值
        """
        self.method = method
        self.top_n = top_n
        self.p_threshold = p_threshold
        self.selected_features_ = None

    def fit(self, X, duration_col='survival_time', event_col='event'):
        """
        拟合特征选择器

        参数:
        - X: 包含特征、生存时间和事件指示器的数据框
        - duration_col: 生存时间列名
        - event_col: 事件指示器列名

        返回:
        - self
        """
        logger.info(f"开始特征选择，方法: {self.method}")

        # 获取特征列
        feature_cols = [col for col in X.columns
                       if col not in [duration_col, event_col]]

        if self.method == 'cox_pvalue':
            self.selected_features_ = self._select_by_cox_pvalue(
                X, feature_cols, duration_col, event_col
            )
        elif self.method == 'univariate':
            self.selected_features_ = self._select_by_univariate(
                X, feature_cols, duration_col, event_col
            )
        elif self.method == 'lasso':
            self.selected_features_ = self._select_by_lasso(
                X, feature_cols, duration_col, event_col
            )
        else:
            raise ValueError(f"Unknown method: {self.method}")

        logger.info(f"特征选择完成，选择了 {len(self.selected_features_)} 个特征")
        return self

    def _select_by_cox_pvalue(self, X, feature_cols, duration_col, event_col):
        """基于 Cox 模型 p 值选择特征"""
        # 对每个特征单独拟合 Cox 模型
        feature_pvalues = []

        logger.info(f"开始评估 {len(feature_cols)} 个候选特征")

        for feature in feature_cols:
            try:
                data = X[[feature, duration_col, event_col]].dropna()
                if len(data) < 10:  # 数据太少跳过
                    logger.debug(f"特征 {feature} 数据太少，跳过")
                    continue

                # 检查特征是否为常数
                if data[feature].nunique() <= 1:
                    logger.debug(f"特征 {feature} 为常数，跳过")
                    continue

                cph = CoxPHFitter()
                cph.fit(data, duration_col=duration_col, event_col=event_col)

                p_value = cph.summary.loc[feature, 'p']
                coef = abs(cph.summary.loc[feature, 'coef'])
                feature_pvalues.append((feature, p_value, coef))

                logger.debug(f"特征 {feature}: p={p_value:.4f}, coef={coef:.4f}")

            except Exception as e:
                logger.debug(f"特征 {feature} 评估失败: {e}")
                continue

        logger.info(f"成功评估了 {len(feature_pvalues)} 个特征")

        # 按 p 值排序
        feature_pvalues.sort(key=lambda x: x[1])

        # 首先尝试使用原始阈值
        selected = [f for f, p, c in feature_pvalues if p < self.p_threshold]

        # 如果选择的特征太少，使用更宽松的策略
        if len(selected) < min(5, len(feature_cols)):
            logger.warning(f"使用原始阈值 {self.p_threshold} 只选择了 {len(selected)} 个特征，采用宽松策略")

            # 策略1：使用更宽松的 p 值阈值
            relaxed_threshold = min(0.2, self.p_threshold * 10)
            selected = [f for f, p, c in feature_pvalues if p < relaxed_threshold]

            # 策略2：如果还是太少，直接选择前 top_n 个
            if len(selected) < min(3, len(feature_cols)):
                logger.warning(f"宽松阈值仍然只选择了 {len(selected)} 个特征，直接选择前 {self.top_n} 个")
                selected = [f for f, p, c in feature_pvalues[:self.top_n]]

        # 限制特征数量
        if len(selected) > self.top_n:
            # 重新按 p 值排序选择最好的
            selected_with_pvalues = [(f, p) for f, p, c in feature_pvalues if f in selected]
            selected_with_pvalues.sort(key=lambda x: x[1])
            selected = [f for f, p in selected_with_pvalues[:self.top_n]]

        logger.info(f"最终选择了 {len(selected)} 个特征")
        if selected:
            logger.info(f"选择的特征: {selected[:5]}{'...' if len(selected) > 5 else ''}")

        return selected

    def _select_by_univariate(self, X, feature_cols, duration_col, event_col):
        """基于单变量统计选择特征"""
        # 使用 F 统计量选择特征
        selector = SelectKBest(score_func=f_regression, k=self.top_n)

        X_features = X[feature_cols]
        y = X[duration_col]  # 使用生存时间作为目标变量

        selector.fit(X_features, y)
        selected_mask = selector.get_support()

        return [feature_cols[i] for i, selected in enumerate(selected_mask) if selected]

    def _select_by_lasso(self, X, feature_cols, duration_col, event_col):
        """基于 Lasso Cox 模型选择特征"""
        # 使用高正则化的 Cox 模型进行特征选择
        cph = CoxPHFitter(penalizer=1.0, l1_ratio=1.0)

        try:
            cph.fit(X, duration_col=duration_col, event_col=event_col)

            # 选择系数非零的特征
            coeffs = cph.params_
            selected = [feature for feature in feature_cols
                       if abs(coeffs.get(feature, 0)) > 1e-6]

            # 如果选择的特征太多，按系数绝对值排序
            if len(selected) > self.top_n:
                feature_coeffs = [(f, abs(coeffs[f])) for f in selected]
                feature_coeffs.sort(key=lambda x: x[1], reverse=True)
                selected = [f for f, _ in feature_coeffs[:self.top_n]]

            return selected

        except Exception:
            # 如果 Lasso 失败，回退到 p 值方法
            logger.warning("Lasso 特征选择失败，回退到 p 值方法")
            return self._select_by_cox_pvalue(X, feature_cols, duration_col, event_col)

    def transform(self, X):
        """转换数据，只保留选择的特征"""
        if self.selected_features_ is None:
            raise ValueError("特征选择器尚未拟合")

        return X[self.selected_features_]

    def fit_transform(self, X, duration_col='survival_time', event_col='event'):
        """拟合并转换数据"""
        return self.fit(X, duration_col, event_col).transform(X)
