# 导入必要的库
from data.selection_bias import gen_selection_bias_data
from algorithm.DWR import DWR
from algorithm.SRDO import SRDO
from model.linear import get_algorithm_class
from metrics import get_metric_class
from utils import setup_seed, get_beta_s, get_expname, calc_var, pretty, get_cov_mask, BV_analysis
from Logger import Logger
from model.STG import STG
from sksurv.metrics import brier_score, cumulative_dynamic_auc
from sklearn.metrics import mean_squared_error, f1_score, r2_score
import numpy as np
import argparse
import os
import torch
from collections import defaultdict as dd
import pandas as pd

from sklearn.preprocessing import StandardScaler

from lifelines import CoxPHFitter
import seaborn as sns
import matplotlib.pyplot as plt
from lifelines.statistics import logrank_test
from lifelines import KaplanMeierFitter
from sksurv.util import Surv
from lifelines.utils import concordance_index
from sklearn.metrics import roc_auc_score
from sklearn.metrics import accuracy_score
import logging
# 新增导入
from sklearn.model_selection import StratifiedKFold

import warnings
warnings.filterwarnings('ignore')

# --- 定义常量 ---
duration_col = 'duration'
event_col = 'event'
SN_col = 'SN_Common'

def get_args():
    parser = argparse.ArgumentParser(description="Script to launch sample reweighting experiments",
                                     formatter_class=argparse.ArgumentDefaultsHelpFormatter)

    # data generation
    parser.add_argument("--p", type=int, default=10, help="Input dim")
    parser.add_argument("--n", type=int, default=2000, help="Sample size")
    parser.add_argument("--V_ratio", type=float, default=0.5)
    parser.add_argument("--Vb_ratio", type=float, default=0.1)
    parser.add_argument("--true_func", choices=["linear", ], default="linear")
    parser.add_argument("--mode", choices=["S_|_V", "S->V", "V->S", "collinearity"], default="S_|_V")
    parser.add_argument("--misspe", choices=["poly", "exp", "None"], default="poly")
    parser.add_argument("--corr_s", type=float, default=0.9)
    parser.add_argument("--corr_v", type=float, default=0.1)
    parser.add_argument("--mms_strength", type=float, default=1.0, help="model misspecifction strength")
    parser.add_argument("--spurious", choices=["nonlinear", "linear"], default="nonlinear")
    parser.add_argument("--r_train", type=float, default=2.5, help="Input dim")
    parser.add_argument("--r_list", type=float, nargs="+", default=[-3, -2, -1.7, -1.5, -1.3, 1.3, 1.5, 1.7, 2, 3])
    parser.add_argument("--noise_variance", type=float, default=0.3)

    # frontend reweighting
    parser.add_argument("--reweighting", choices=["None", "DWR", "SRDO"], default="SRDO")
    parser.add_argument("--decorrelation_type", choices=["global", "group"], default="global")
    parser.add_argument("--order", type=int, default=1)
    parser.add_argument("--iters_balance", type=int, default=2000)

    parser.add_argument("--topN", type=int, default=5)
    # backend model
    parser.add_argument("--backend",
                        choices=["OLS", "Lasso", "Ridge", "Weighted_cox", "LogLogistic", "Weibull", "LogNormal"],
                        default="Ridge")
    parser.add_argument("--paradigm", choices=["regr", "fs", ], default="regr")
    parser.add_argument("--iters_train", type=int, default=5000)
    parser.add_argument("--lam_backend", type=float, default=0.03)  # regularizer coefficient
    parser.add_argument("--fs_type", choices=["oracle", "None", "given", "STG"], default="STG")
    parser.add_argument("--mask_given", type=int, nargs="+", default=[1, 1, 1, 1, 1, 0, 0, 0, 0, 0])
    parser.add_argument("--mask_threshold", type=float, default=0.2)
    parser.add_argument("--lam_STG", type=float, default=3)
    parser.add_argument("--sigma_STG", type=float, default=0.1)
    parser.add_argument("--metrics", nargs="+", default=["L1_beta_error", "L2_beta_error"])
    parser.add_argument("--bv_analysis", action="store_true")

    # others
    parser.add_argument("--seed", type=int, default=2)
    parser.add_argument("--times", type=int, default=10)
    parser.add_argument("--result_dir", default="results")

    # --- 新增参数 ---
    parser.add_argument("--n_splits", type=int, default=5, help="Number of folds for cross-validation")

    return parser.parse_args()


def main(args, round, logger):
    logging.info(f"--- Starting Round {round} with Seed {args.seed + round} ---")
    setup_seed(args.seed + round)
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

    # --- 1. 数据加载和初始预处理 ---
    PATH = r'processed_equipment_data.csv'
    try:
        data = pd.read_csv(PATH)
        logging.info(f"Successfully loaded data from {PATH}. Shape: {data.shape}")
    except FileNotFoundError:
        logging.error(f"Data file not found at {PATH}. Please check path.")
        return {}  # 返回空字典以避免在主循环中出错

    if SN_col in data.columns: data = data.drop(columns=[SN_col])
    problematic_cols = ['0A_Flattop', 'Ambient_HST_delta', 'Ambient_temp', 'Offset_cal']
    cols_to_drop = [col for col in problematic_cols if col in data.columns]
    data = data.drop(columns=cols_to_drop)
    # logging.info(f"Data shape after cleaning: {data.shape}")
    feature_cols = pd.read_csv(r'C:\Users\<USER>\OneDrive - CentraleSupelec\2025_Doc\For_2025_06_23\selected_features.csv')['0'].values.tolist()
    # feature_cols = pd.read_csv(r'C:\Users\<USER>\OneDrive - CentraleSupelec\2025_Doc\For_2025_06_23\feature_names.csv')['feature_name'].values.tolist()

    if feature_cols:
        logging.info(f"Succsessfully loaded feature columns. Total: {len(feature_cols)}")

    p = data.shape[1] -2

    # feature_cols = [col for col in data.columns if col not in [duration_col, event_col]]

    if feature_cols:
        logging.info(f"Succsessfully get feature columns. Total: {len(feature_cols)}")
    else:
        logging.error("No feature columns could be identified. Exiting.")
        return {}  # 返回空字典以避免在主循环中出错

    # --- 2. 设置交叉验证 ---
    skf = StratifiedKFold(n_splits=args.n_splits, shuffle=True, random_state=args.seed + round)
    X, y = data[feature_cols], data[event_col]
    c_indices = []

    # --- 3. 开始交叉验证循环 ---
    for fold, (train_idx, val_idx) in enumerate(skf.split(X, y)):
        logging.info(f"--- Fold {fold + 1}/{args.n_splits} ---")

        # training_pd_data_fold = data.iloc[train_idx].copy()
        # validation_pd_data_fold = data.iloc[val_idx].copy()

        train_data_fold = data.iloc[train_idx]
        val_data_fold = data.iloc[val_idx]

        # --- 新增：数据标准化 ---
        scaler = StandardScaler()

        # 1. 只在当前折的训练集上 fit scaler
        #    注意：我们只对特征列进行标准化
        scaler.fit(train_data_fold[feature_cols])

        # 2. 创建副本以避免 SettingWithCopyWarning
        training_pd_data_fold = train_data_fold.copy()
        validation_pd_data_fold = val_data_fold.copy()

        # 3. 使用 fit 好的 scaler 来 transform 训练集和验证集
        training_pd_data_fold[feature_cols] = scaler.transform(train_data_fold[feature_cols])
        validation_pd_data_fold[feature_cols] = scaler.transform(val_data_fold[feature_cols])
        logging.info("Features for this fold have been standardized.")

        X_train_fold_np = training_pd_data_fold[feature_cols].values
        n_fold, p_fold = X_train_fold_np.shape

        # --- 4. Reweighting ---
        W = np.ones((n_fold, 1))
        if args.reweighting == "DWR":
            # 假设DWR返回 (n, 1) 的numpy数组
            W = DWR(X_train_fold_np, logger=logger, device=device)
        elif args.reweighting == "SRDO":
            logging.info("Using SRDO reweighting")
            p_s_fold = p_fold // 2
            # 假设SRDO返回 (n, 1) 的numpy数组
            W = SRDO(X_train_fold_np, p_s_fold, hidden_layer_sizes=(64, 16), decorrelation_type=args.decorrelation_type,
                     max_iter=args.iters_balance)
            logging.info("SRDO reweighting completed.")

        if args.reweighting != "None":
            logging.info(f"Weights calculated and normalized for fold {fold + 1}.")

        logging.info("Weight statistics - mean: %.6f, min: %.6f, max: %.6f", np.mean(W), np.min(W), np.max(W))
        mean_value = max(np.mean(W), 0)
        W = W * (1 / mean_value)
        logging.info("After normalization - max: %.6f, min: %.6f", np.max(W), np.min(W))

        logging.info(f"W before clip: min={W.min():.6f}, max={W.max():.6f}, mean={W.mean():.6f}, std={W.std():.6f}")

        # import matplotlib.pyplot as plt
        # plt.figure()
        # plt.hist(W.flatten(), bins=50)
        # plt.xlabel("W value")
        # plt.ylabel("Frequency")
        # plt.title("Distribution of W before clip")
        # # plt.savefig(f"W_hist_before_clip_fold{fold + 1}.png")
        # plt.close()
        # logging.info(f"W histogram before clip saved as W_hist_before_clip_fold{fold + 1}.png")

        # clip之后
        W = np.clip(W, 1e-8, np.max(W))
        logging.info(f"W after clip: min={W.min():.6f}, max={W.max():.6f}, mean={W.mean():.6f}, std={W.std():.6f}")

        # plt.figure()
        # plt.hist(W.flatten(), bins=50)
        # plt.xlabel("W value")
        # plt.ylabel("Frequency")
        # plt.title("Distribution of W after clip")
        # # plt.savefig(f"W_hist_after_clip_fold{fold + 1}.png")
        # plt.close()
        # logging.info(f"W histogram after clip saved as W_hist_after_clip_fold{fold + 1}.png")

        # 检验X.dot(W)是否每一列之间的correlation已经很小了
        # XW = X_train_fold_np * W # (n, p) * (n, 1) => (n, p)
        # print(X_train_fold_np.shape, W.shape, XW.shape)
        # weighted_X = X_train_fold_np * W  # (n, p)
        # weighted_mean = np.sum(weighted_X, axis=0) / np.sum(W)
        # weighted_X_centered = weighted_X - weighted_mean
        # weighted_cov = (weighted_X_centered * W).T @ weighted_X_centered / np.sum(W)
        # weighted_corr = weighted_cov / (
        #             np.sqrt(np.diag(weighted_cov))[:, None] * np.sqrt(np.diag(weighted_cov))[None, :])
        # np.fill_diagonal(weighted_corr, 0)
        # max_corr = np.abs(weighted_corr).max()
        # logging.info(f"Max correlation after reweighting: {max_corr:.4f}")
        # --- 5. 模型训练 ---
        if args.paradigm == "regr":
            logging.info("Starting regression paradigm")
            logging.info(f"Training backend model '{args.backend}' on fold {fold + 1}...")
            mask = [True, ] * p
            if args.backend in ["Weighted_cox", "LogLogistic", "Weibull", "LogNormal"]:
                logging.info(f"Using penalizer {args.lam_backend} for Weighted_cox")
                model_func = get_algorithm_class(args.backend)
                model = model_func(
                    X=training_pd_data_fold[feature_cols + [duration_col, event_col]],
                    duration_col=duration_col,
                    event_col=event_col,
                    W=W,
                    pen=args.lam_backend,
                    **vars(args)
                )
            elif args.backend in ["OLS", "Lasso", "Ridge"]:
                model_func = get_algorithm_class(args.backend)
                model = model_func(
                    X=training_pd_data_fold[feature_cols],
                    Y=training_pd_data_fold[[duration_col, event_col]],
                    W=W,
                    **vars(args)
                )
            else:
                raise NotImplementedError(f"Backend '{args.backend}' not implemented for regression paradigm.")

            # --- CORRECTION PART 1: 评估训练集分数 ---
            # 在评估前，必须为DataFrame添加模型期望的'Weights'列



        else:
            raise NotImplementedError(f"Paradigm '{args.paradigm}' requires adaptation for CV.")

        if args.backend in ["Weighted_cox", "LogLogistic", "Weibull", "LogNormal"]:
            summary = model.summary
            # print("summary", summary)

            coef = summary["coef"]
            # print("training:")
            # plot_KM(cph2, sorted_indices, coef, X_train_pd, int(args.topN), "stable_cox_regression_nature.jpg")

            training_data_for_score = training_pd_data_fold.copy()
            training_data_for_score['Weights'] = W.flatten()  # 使用训练时的权重
            train_c_index = model.score(training_data_for_score, scoring_method='concordance_index')
            logging.info(f"Fold {fold + 1} Training C-index: {train_c_index:.4f}")
            # --- 6. 模型评估 ---
            logging.info(f"Evaluating model on validation set of fold {fold + 1}...")

            # --- CORRECTION PART 2: 评估验证集分数 ---
            # 验证集也需要一个'Weights'列，但权重应全部为1
            Weights = np.ones((validation_pd_data_fold.shape[0], 1))
            validation_data_for_score = np.concatenate((validation_pd_data_fold, Weights), axis=1)
            validation_data_for_score = pd.DataFrame(validation_data_for_score, columns=list(validation_pd_data_fold.columns) + ['Weights'])
            val_c_index = model.score(validation_data_for_score, scoring_method='concordance_index')
            c_indices.append(val_c_index)
            logging.info(f"Fold {fold + 1} Validation C-index: {val_c_index:.4f}")

        elif args.backend in ["OLS", "Lasso", "Ridge"]:
            r2_scores = []
            rmses = []
            mapes = []
            f1_scores = []
            training_data_for_score = training_pd_data_fold.copy()
            # 使用duration和event作为Y值进行训练
            # 将W作为特征的一部分
            training_features = np.concatenate((training_data_for_score[feature_cols], W), axis=1)
            model.fit(training_features, 
                     training_data_for_score[[duration_col, event_col]])
            
            # 分别计算duration和event的预测结果 - 使用包含权重的特征
            train_features_with_weights = np.concatenate((training_data_for_score[feature_cols], W), axis=1)
            train_pred = model.predict(train_features_with_weights)
            train_duration_pred = train_pred[:, 0]  # 第一列是duration的预测
            train_event_pred = train_pred[:, 1]     # 第二列是event的预测
            
            # 计算训练集的评估指标 - 不再使用sample_weight
            train_r2 = r2_score(training_data_for_score[duration_col], train_duration_pred)
            train_rmse = np.sqrt(mean_squared_error(training_data_for_score[duration_col], train_duration_pred))
            train_mape = np.mean(np.abs((training_data_for_score[duration_col] - train_duration_pred) / 
                                      training_data_for_score[duration_col]))
            
            # F1 score计算
            f1_scores.append(f1_score(training_data_for_score[event_col], train_event_pred > 0.5))
            
            logging.info(f"Fold {fold + 1} Training R2: {train_r2:.4f}, RMSE: {train_rmse:.4f}, MAPE: {train_mape:.4f}, F1: {f1_scores[-1]:.4f}")

            # 验证集评估 - 使用全1权重
            Weights = np.ones((validation_pd_data_fold.shape[0], 1))
            val_features_with_weights = np.concatenate((validation_pd_data_fold[feature_cols], Weights), axis=1)
            val_pred = model.predict(val_features_with_weights)
            val_duration_pred = val_pred[:, 0]
            val_event_pred = val_pred[:, 1]
            
            # 验证集评估指标计算
            val_r2 = r2_score(validation_pd_data_fold[duration_col], val_duration_pred)
            val_rmse = np.sqrt(mean_squared_error(validation_pd_data_fold[duration_col], val_duration_pred))
            val_mape = np.mean(np.abs((validation_pd_data_fold[duration_col] - val_duration_pred) / 
                                    validation_pd_data_fold[duration_col]))
            val_f1 = f1_score(validation_pd_data_fold[event_col], val_event_pred > 0.5)

            r2_scores.append(val_r2)
            rmses.append(val_rmse)
            mapes.append(val_mape)
            f1_scores.append(val_f1)
            logging.info(f"Fold {fold + 1} Validation R2: {val_r2:.4f}, RMSE: {val_rmse:.4f}, MAPE: {val_mape:.4f}, F1: {val_f1:.4f}")

    # --- 7. 聚合交叉验证结果 ---
    if args.backend in ["Weighted_cox", "LogLogistic", "Weibull", "LogNormal"]:
        if not c_indices:
            logging.warning("No folds were processed. Returning empty results.")
            return {}

        mean_c_index, std_c_index, worst_c_index = np.mean(c_indices), np.std(c_indices), np.min(c_indices)
        logging.info("--- Cross-Validation Summary for this Round ---")
        logging.info(f"C-indices per fold: {[f'{c:.4f}' for c in c_indices]}")
        logging.info(f"Mean C-index: {mean_c_index:.4f}")
        logging.info(f"Std Dev of C-index: {std_c_index:.4f}")
        logging.info(f"Worst C-index: {worst_c_index:.4f}")

        return {"mean_c_index": mean_c_index, "std_c_index": std_c_index, "worst_c_index": worst_c_index}

    elif args.backend in ["OLS", "Lasso", "Ridge"]:
        if not r2_scores:
            logging.warning("No folds were processed. Returning empty results.")
            return {}

        mean_r2, std_r2, worst_r2 = np.mean(r2_scores), np.std(r2_scores), np.min(r2_scores)
        mean_rmse, std_rmse, worst_rmse = np.mean(rmses), np.std(rmses), np.min(rmses)
        mean_mape, std_mape, worst_mape = np.mean(mapes), np.std(mapes), np.min(mapes)
        mean_f1, std_f1, worst_f1 = np.mean(f1_scores), np.std(f1_scores), np.min(f1_scores)

        logging.info("--- Cross-Validation Summary for this Round ---")
        logging.info(f"R2 scores per fold: {[f'{r2:.4f}' for r2 in r2_scores]}, Mean: {mean_r2:.4f}, Std: {std_r2:.4f}, Worst: {worst_r2:.4f}")
        logging.info(f"RMSE scores per fold: {[f'{rmse:.4f}' for rmse in rmses]}, Mean: {mean_rmse:.4f}, Std: {std_rmse:.4f}, Worst: {worst_rmse:.4f}")
        logging.info(f"MAPE scores per fold: {[f'{mape:.4f}' for mape in mapes]}, Mean: {mean_mape:.4f}, Std: {std_mape:.4f}, Worst: {worst_mape:.4f}")
        logging.info(f"F1 scores per fold: {[f'{f1:.4f}' for f1 in f1_scores]}, Mean: {mean_f1:.4f}, Std: {std_f1:.4f}, Worst: {worst_f1:.4f}")

        return {"mean_r2": mean_r2, "std_r2": std_r2, "worst_r2": worst_r2,
                "mean_rmse": mean_rmse, "std_rmse": std_rmse, "worst_rmse": worst_rmse,
                "mean_mape": mean_mape, "std_mape": std_mape, "worst_mape": worst_mape,
                "mean_f1": mean_f1, "std_f1": std_f1, "worst_f1": worst_f1}



if __name__ == "__main__":
    args = get_args()
    setup_seed(args.seed)
    expname = get_expname(args)
    os.makedirs(os.path.join(args.result_dir, expname), exist_ok=True)
    logger = Logger(args)
    logger.log_args(args)

    results_list = dd(list)
    for i in range(args.times):
        logger.info("\n \n =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round %d =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=" % i)
        results_per_run = main(args, i, logger)
        if results_per_run:  # 确保main函数成功运行
            for k, v in results_per_run.items():
                results_list[k].append(v)

    # 将所欲的results_list保存到csv文件中
    # with open(os.path.join(r"C:\Users\<USER>\OneDrive - CentraleSupelec\2025_Doc\For_2025_06_23", 'results.csv'), 'w') as f:
    #     for k, v in results_list.items():
    #         f.write(f"{k},{v}\n")
    if args.backend in ["Weighted_cox", "LogLogistic", "Weibull", "LogNormal"]:
        if results_list:
            logging.info("=" * 50)
            logging.info(f"Final Summary Over {args.times} Independent Run(s)")
            logging.info("=" * 50)

            # 遍历聚合后的结果，例如 'mean_c_index'
            for metric_name, value_list in results_list.items():
                # 计算多次运行的平均值和标准差
                final_mean = np.mean(value_list)
                final_std = np.std(value_list)

                # 打印格式化的结果
                # 例如: "Average of 'mean_c_index' over 10 runs: 0.5012 (Std Dev: 0.0050)"
                logging.info(
                    f"Average of '{metric_name}' over {args.times} runs: {final_mean:.4f} (Std Dev: {final_std:.4f})")
        else:
            logging.info("No results were generated from the runs.")

    elif args.backend in ["OLS", "Lasso", "Ridge"]:
        if results_list:
            logging.info("=" * 50)
            logging.info(f"Final Summary Over {args.times} Independent Run(s)")
            logging.info("=" * 50)

            # 遍历聚合后的结果，例如 'mean_r2'
            for metric_name, value_list in results_list.items():
                # 计算多次运行的平均值和标准差
                final_mean = np.mean(value_list)
                final_std = np.std(value_list)

                # 打印格式化的结果
                # 例如: "Average of 'mean_r2' over 10 runs: 0.5012 (Std Dev: 0.0050)"
                logging.info(
                    f"Average of '{metric_name}' over {args.times} runs: {final_mean:.4f} (Std Dev: {final_std:.4f})")
        else:
            logging.info("No results were generated from the runs.")